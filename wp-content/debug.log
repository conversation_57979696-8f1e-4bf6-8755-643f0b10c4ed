[09-Oct-2024 00:41:27 UTC] PHP Warning:  Undefined array key 1 in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/class-plugin-optin.php on line 88
[09-Oct-2024 00:41:27 UTC] PHP Deprecated:  strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/class-plugin-optin.php on line 88
[09-Oct-2024 00:41:27 UTC] WordPress database error Table 'pocprints.wp_trp_dictionary_pt_br_' doesn't exist for query SELECT COUNT(id) FROM wp_trp_dictionary_pt_br_ WHERE translated != '' made by do_action('admin_init'), WP_Hook->do_action, WP_Hook->apply_filters, TRP_Plugin_Optin->process_optin_actions
[09-Oct-2024 00:41:29 UTC] PHP Warning:  Undefined array key "tp-add-on-seo-pack/tp-seo-pack.php" in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/advanced-settings/load-legacy-seo-pack.php on line 7
[09-Oct-2024 00:41:30 UTC] PHP Warning:  Undefined array key "tp-add-on-seo-pack/tp-seo-pack.php" in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/advanced-settings/load-legacy-seo-pack.php on line 7
[09-Oct-2024 00:51:08 UTC] PHP Warning:  Undefined array key "tp-add-on-seo-pack/tp-seo-pack.php" in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/advanced-settings/load-legacy-seo-pack.php on line 7
[09-Oct-2024 00:51:08 UTC] PHP Warning:  Undefined array key "tp-add-on-seo-pack/tp-seo-pack.php" in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/advanced-settings/load-legacy-seo-pack.php on line 7
[09-Oct-2024 17:10:27 UTC] PHP Warning:  Undefined array key "old" in /home/<USER>/public_html/wp-content/plugins/wp-fail2ban/lib/site-health.php on line 619
[02-Jul-2025 01:46:33 UTC] PHP Fatal error:  Uncaught Error: Class "WPC_Product_Integration" not found in /var/www/html/wp-content/plugins/woocommerce-product-customizer/woocommerce-product-customizer.php:115
Stack trace:
#0 /var/www/html/wp-includes/class-wp-hook.php(324): WooCommerce_Product_Customizer->init_woocommerce_integration('')
#1 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#2 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /var/www/html/wp-settings.php(578): do_action('plugins_loaded')
#4 /var/www/html/wp-config.php(144): require_once('/var/www/html/w...')
#5 /var/www/html/wp-load.php(50): require_once('/var/www/html/w...')
#6 /var/www/html/wp-admin/admin.php(35): require_once('/var/www/html/w...')
#7 /var/www/html/wp-admin/plugins.php(10): require_once('/var/www/html/w...')
#8 {main}
  thrown in /var/www/html/wp-content/plugins/woocommerce-product-customizer/woocommerce-product-customizer.php on line 115
[02-Jul-2025 01:46:33 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>woocommerce</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 01:48:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 01:48:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 01:59:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:01:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:01:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:15:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:15:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:18:04 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:18:06 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:19:47 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:19:49 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:19:54 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:21:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:21:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:21:20 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:22:29 UTC] PHP Warning:  wp_version_check(): Ocorreu um erro inesperado. Algo pode estar errado com o WordPress.org ou com a configuração deste servidor. Se os problemas persistirem, busque ajuda no <a href="https://br.wordpress.org/support/forums/">fórum de suporte</a>. (O WordPress não conseguiu estabelecer uma conexão segura com o WordPress.org. Contate o administrador do servidor.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:22:35 UTC] PHP Warning:  wp_update_themes(): Ocorreu um erro inesperado. Algo pode estar errado com o WordPress.org ou com a configuração deste servidor. Se os problemas persistirem, busque ajuda no <a href="https://br.wordpress.org/support/forums/">fórum de suporte</a>. (O WordPress não conseguiu estabelecer uma conexão segura com o WordPress.org. Contate o administrador do servidor.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:22:58 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:23:00 UTC] PHP Warning:  foreach() argument must be of type array|object, string given in /var/www/html/wp-content/plugins/superfrete/api/Http/Request.php on line 75
[02-Jul-2025 02:23:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:23:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:23:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:23:49 UTC] WordPress database error You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'FOREIGN KEY (customization_id) REFERENCES wp_wpc_customizations(id) ON DELETE...' at line 1 for query ALTER TABLE wp_wpc_customization_fields ADD COLUMN FOREIGN KEY (customization_id) REFERENCES wp_wpc_customizations(id) ON DELETE CASCADE made by activate_plugin, do_action('activate_woocommerce-product-customizer/woocommerce-product-customizer.php'), WP_Hook->do_action, WP_Hook->apply_filters, WooCommerce_Product_Customizer->activate, WPC_Database::create_tables, dbDelta
[02-Jul-2025 02:24:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:24:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:24:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:17 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:19 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:20 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:21 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:24:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:24:34 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:35 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:36 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:37 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:38 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:40 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:41 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:50 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:28:10 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:28:10 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:28:10 UTC] PHP Fatal error:  Uncaught Error: Class "WPC_Product_Integration" not found in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php:244
Stack trace:
#0 /var/www/html/wp-includes/class-wp-hook.php(324): WPC_Frontend->enqueue_frontend_scripts('')
#1 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#2 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /var/www/html/wp-includes/script-loader.php(2299): do_action('wp_enqueue_scri...')
#4 /var/www/html/wp-includes/class-wp-hook.php(324): wp_enqueue_scripts('')
#5 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#6 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#7 /var/www/html/wp-includes/general-template.php(3192): do_action('wp_head')
#8 /var/www/html/wp-content/themes/storefront/header.php(18): wp_head()
#9 /var/www/html/wp-includes/template.php(810): require_once('/var/www/html/w...')
#10 /var/www/html/wp-includes/template.php(745): load_template('/var/www/html/w...', true, Array)
#11 /var/www/html/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)
#12 /var/www/html/wp-content/plugins/woocommerce/templates/single-product.php(22): get_header('shop')
#13 /var/www/html/wp-includes/template-loader.php(106): include('/var/www/html/w...')
#14 /var/www/html/wp-blog-header.php(19): require_once('/var/www/html/w...')
#15 /var/www/html/index.php(17): require('/var/www/html/w...')
#16 {main}
  thrown in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php on line 244
[02-Jul-2025 02:28:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:28:11 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:31:43 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:31:43 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:31:44 UTC] PHP Fatal error:  Uncaught Error: Call to a member function get_id() on string in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php:242
Stack trace:
#0 /var/www/html/wp-includes/class-wp-hook.php(324): WPC_Frontend->enqueue_frontend_scripts('')
#1 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#2 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /var/www/html/wp-includes/script-loader.php(2299): do_action('wp_enqueue_scri...')
#4 /var/www/html/wp-includes/class-wp-hook.php(324): wp_enqueue_scripts('')
#5 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#6 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#7 /var/www/html/wp-includes/general-template.php(3192): do_action('wp_head')
#8 /var/www/html/wp-content/themes/storefront/header.php(18): wp_head()
#9 /var/www/html/wp-includes/template.php(810): require_once('/var/www/html/w...')
#10 /var/www/html/wp-includes/template.php(745): load_template('/var/www/html/w...', true, Array)
#11 /var/www/html/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)
#12 /var/www/html/wp-content/plugins/woocommerce/templates/single-product.php(22): get_header('shop')
#13 /var/www/html/wp-includes/template-loader.php(106): include('/var/www/html/w...')
#14 /var/www/html/wp-blog-header.php(19): require_once('/var/www/html/w...')
#15 /var/www/html/index.php(17): require('/var/www/html/w...')
#16 {main}
  thrown in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php on line 242
[02-Jul-2025 02:31:44 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:31:44 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:33:25 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:33:25 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:33:26 UTC] PHP Fatal error:  Uncaught Error: Call to a member function get_id() on string in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php:242
Stack trace:
#0 /var/www/html/wp-includes/class-wp-hook.php(324): WPC_Frontend->enqueue_frontend_scripts('')
#1 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#2 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /var/www/html/wp-includes/script-loader.php(2299): do_action('wp_enqueue_scri...')
#4 /var/www/html/wp-includes/class-wp-hook.php(324): wp_enqueue_scripts('')
#5 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#6 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#7 /var/www/html/wp-includes/general-template.php(3192): do_action('wp_head')
#8 /var/www/html/wp-content/themes/storefront/header.php(18): wp_head()
#9 /var/www/html/wp-includes/template.php(810): require_once('/var/www/html/w...')
#10 /var/www/html/wp-includes/template.php(745): load_template('/var/www/html/w...', true, Array)
#11 /var/www/html/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)
#12 /var/www/html/wp-content/plugins/woocommerce/templates/single-product.php(22): get_header('shop')
#13 /var/www/html/wp-includes/template-loader.php(106): include('/var/www/html/w...')
#14 /var/www/html/wp-blog-header.php(19): require_once('/var/www/html/w...')
#15 /var/www/html/index.php(17): require('/var/www/html/w...')
#16 {main}
  thrown in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php on line 242
[02-Jul-2025 02:33:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:33:26 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:33:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:33:29 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:33:29 UTC] PHP Fatal error:  Uncaught Error: Call to a member function get_id() on string in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php:242
Stack trace:
#0 /var/www/html/wp-includes/class-wp-hook.php(324): WPC_Frontend->enqueue_frontend_scripts('')
#1 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#2 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /var/www/html/wp-includes/script-loader.php(2299): do_action('wp_enqueue_scri...')
#4 /var/www/html/wp-includes/class-wp-hook.php(324): wp_enqueue_scripts('')
#5 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#6 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#7 /var/www/html/wp-includes/general-template.php(3192): do_action('wp_head')
#8 /var/www/html/wp-content/themes/storefront/header.php(18): wp_head()
#9 /var/www/html/wp-includes/template.php(810): require_once('/var/www/html/w...')
#10 /var/www/html/wp-includes/template.php(745): load_template('/var/www/html/w...', true, Array)
#11 /var/www/html/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)
#12 /var/www/html/wp-content/plugins/woocommerce/templates/single-product.php(22): get_header('shop')
#13 /var/www/html/wp-includes/template-loader.php(106): include('/var/www/html/w...')
#14 /var/www/html/wp-blog-header.php(19): require_once('/var/www/html/w...')
#15 /var/www/html/index.php(17): require('/var/www/html/w...')
#16 {main}
  thrown in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php on line 242
[02-Jul-2025 02:33:30 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:33:30 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:37:38 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:37:38 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:37:39 UTC] WPC Frontend: display_customization_fields called
[02-Jul-2025 02:37:39 UTC] WPC Frontend: Product ID: 10150
[02-Jul-2025 02:37:39 UTC] WPC Frontend: Enable customization meta: yes
[02-Jul-2025 02:37:39 UTC] WPC Frontend: Product 10150 is customizable - proceeding
[02-Jul-2025 02:37:41 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:37:41 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 13:54:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 13:55:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 13:56:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 13:56:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 13:56:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 15:00:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 15:00:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 15:57:17 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 15:57:17 UTC] WPC Debug - User ID: 1
[02-Jul-2025 15:57:17 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 16:02:09 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 16:02:09 UTC] WPC Debug - User ID: 1
[02-Jul-2025 16:02:09 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 16:10:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:10:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:10:43 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:10:43 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:10:44 UTC] WPC Frontend: test_basic nopriv called
[02-Jul-2025 16:11:10 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:11:10 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:11:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:11:11 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:11:40 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:11:40 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:11:40 UTC] WPC Test Upload - Function called
[02-Jul-2025 16:11:40 UTC] WPC Test Upload - POST nonce: NOT SET
[02-Jul-2025 16:11:50 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:11:50 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:11:50 UTC] WPC Test Upload - Function called
[02-Jul-2025 16:11:50 UTC] WPC Test Upload - POST nonce: f22da9c0f3
[02-Jul-2025 16:12:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:12:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:12:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:12:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:12:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:12:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:13:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:13:11 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:13:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:13:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:15:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:15:11 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:15:34 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:15:34 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:15:50 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:15:50 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:16:18 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:19 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 16:16:19 UTC] WPC Debug - User ID: 1
[02-Jul-2025 16:16:19 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 16:16:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:16:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:25 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:16:25 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:25 UTC] WPC Test Upload - Function called
[02-Jul-2025 16:16:25 UTC] WPC Test Upload - POST nonce: f22da9c0f3
[02-Jul-2025 16:16:30 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:16:30 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:35 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:16:35 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:35 UTC] WPC Upload - Function called
[02-Jul-2025 16:16:35 UTC] WPC Upload - POST nonce: f22da9c0f3
[02-Jul-2025 16:16:35 UTC] WPC Upload - Current user ID: 1
[02-Jul-2025 16:16:35 UTC] WPC Upload - Is user logged in: yes
[02-Jul-2025 16:16:35 UTC] WPC Upload - Nonce check result: VALID
[02-Jul-2025 16:16:35 UTC] WPC Upload - Nonce verified successfully
[02-Jul-2025 16:17:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:17:11 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:18:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:18:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:18:24 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:18:24 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:19:12 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:19:12 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:20:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:20:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:21:12 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:21:12 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:21:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:21:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:21:15 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 16:21:15 UTC] WPC Debug - User ID: 1
[02-Jul-2025 16:21:15 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 16:21:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:21:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:21:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:21:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:21:22 UTC] WPC Upload - Function called
[02-Jul-2025 16:21:22 UTC] WPC Upload - POST nonce: f22da9c0f3
[02-Jul-2025 16:21:22 UTC] WPC Upload - Current user ID: 1
[02-Jul-2025 16:21:22 UTC] WPC Upload - Is user logged in: yes
[02-Jul-2025 16:21:22 UTC] WPC Upload - Nonce check result: VALID
[02-Jul-2025 16:21:22 UTC] WPC Upload - Nonce verified successfully
[02-Jul-2025 16:23:13 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:23:13 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:23:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:23:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:25:12 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:25:12 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:25:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:25:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:25:44 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:25:44 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:25:44 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 16:25:44 UTC] WPC Debug - User ID: 1
[02-Jul-2025 16:25:44 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 16:25:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:25:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:25:51 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:25:51 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:25:51 UTC] WPC Upload - Function called
[02-Jul-2025 16:25:51 UTC] WPC Upload - POST nonce: f22da9c0f3
[02-Jul-2025 16:25:51 UTC] WPC Upload - Current user ID: 1
[02-Jul-2025 16:25:51 UTC] WPC Upload - Is user logged in: yes
[02-Jul-2025 16:25:51 UTC] WPC Upload - Nonce check result: VALID
[02-Jul-2025 16:25:51 UTC] WPC Upload - Nonce verified successfully
[02-Jul-2025 16:27:13 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:27:13 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:27:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:27:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:29:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:29:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:29:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:29:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:31:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:31:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:31:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:31:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:33:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:33:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:33:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:33:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:35:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:35:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:35:17 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:35:17 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:35:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:35:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:37:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:37:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:37:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:37:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:39:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:39:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:39:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:39:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:41:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:41:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:41:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:41:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:43:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:43:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:43:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:43:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:45:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:45:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:45:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:45:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:47:17 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:47:17 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:47:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:47:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:49:17 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:49:17 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:49:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:49:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:51:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:51:18 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:51:21 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:51:21 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:51:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:51:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:53:19 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:53:19 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:53:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:53:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:55:19 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:55:19 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:55:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:55:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:57:20 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:57:20 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:57:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:57:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:59:20 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:59:20 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:59:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:59:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:01:20 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:01:20 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:01:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:01:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:03:21 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:03:21 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:03:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:03:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:05:21 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:05:21 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:05:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:05:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:07:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:07:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:07:24 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:07:24 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:07:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:07:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:09:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:09:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:09:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:09:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:11:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:11:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:11:40 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:11:40 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:11:40 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 17:11:40 UTC] WPC Debug - User ID: 1
[02-Jul-2025 17:11:40 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 17:11:42 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:11:42 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:11:50 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:11:50 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:11:50 UTC] WPC Upload - Function called
[02-Jul-2025 17:11:50 UTC] WPC Upload - POST nonce: f22da9c0f3
[02-Jul-2025 17:11:50 UTC] WPC Upload - Current user ID: 1
[02-Jul-2025 17:11:50 UTC] WPC Upload - Is user logged in: yes
[02-Jul-2025 17:11:50 UTC] WPC Upload - Nonce check result: VALID
[02-Jul-2025 17:11:50 UTC] WPC Upload - Nonce verified successfully
[02-Jul-2025 17:12:54 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:12:54 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:12:54 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 17:12:54 UTC] WPC Debug - User ID: 1
[02-Jul-2025 17:12:54 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 17:12:59 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:12:59 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:13:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:13:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:14:13 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:14:13 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:14:13 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 17:14:13 UTC] WPC Debug - User ID: 1
[02-Jul-2025 17:14:13 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 17:14:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:14:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:14:19 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:14:19 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:14:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:14:26 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:14:26 UTC] WPC Upload - Function called
[02-Jul-2025 17:14:26 UTC] WPC Upload - POST nonce: f22da9c0f3
[02-Jul-2025 17:14:26 UTC] WPC Upload - Current user ID: 1
[02-Jul-2025 17:14:26 UTC] WPC Upload - Is user logged in: yes
[02-Jul-2025 17:14:26 UTC] WPC Upload - Nonce check result: VALID
[02-Jul-2025 17:14:26 UTC] WPC Upload - Nonce verified successfully
[02-Jul-2025 17:15:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:15:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:16:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:16:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:17:24 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:17:24 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:18:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:18:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:19:24 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:19:24 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:20:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:20:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:21:24 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:21:24 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:22:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:22:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:23:24 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:23:24 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:23:28 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:23:28 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:24:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:24:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:25:25 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:25:25 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:26:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:26:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:27:27 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:27:27 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:28:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:28:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:29:25 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:29:25 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:30:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:30:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:31:25 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:31:25 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:32:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:32:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:33:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:33:26 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:34:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:34:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:35:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:35:26 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:36:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:36:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:37:27 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:37:27 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:38:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:38:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:39:27 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:39:27 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:39:30 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:39:30 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:40:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:40:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:41:27 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:41:27 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:42:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:42:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:43:28 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:43:28 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:44:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:44:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:45:28 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:45:28 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:46:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:46:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:47:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:47:29 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:48:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:48:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:49:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:49:29 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:50:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:50:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:51:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:51:29 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:52:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:52:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:53:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:53:29 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:54:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:54:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:55:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:55:29 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:55:33 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:55:33 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:56:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:56:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:57:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:57:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:58:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:58:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 17:59:31 UTC] WPC Frontend: Constructor called
[02-Jul-2025 17:59:31 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:00:17 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:00:17 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:01:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:01:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:02:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:02:18 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:03:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:03:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:04:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:04:18 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:05:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:05:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:06:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:06:18 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:07:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:07:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:08:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:08:18 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:09:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:09:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:10:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:10:18 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:11:33 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:11:33 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:11:37 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:11:37 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:12:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:12:18 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:13:33 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:13:33 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:14:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:14:18 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:15:33 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:15:33 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:16:19 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:16:19 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:17:34 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:17:34 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:18:19 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:18:19 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:19:34 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:19:34 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:20:19 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:20:19 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:21:35 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:21:35 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:22:20 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:22:20 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:23:35 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:23:35 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:24:21 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:24:21 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:25:36 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:25:36 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:26:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:26:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:27:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:27:39 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:27:41 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:27:41 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:28:21 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:28:21 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:29:37 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:29:37 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:30:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:30:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:31:38 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:31:38 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:32:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:32:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:33:38 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:33:38 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:34:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:34:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:35:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:35:39 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:36:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:36:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:37:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:37:39 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:38:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:38:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:39:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:39:39 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:40:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:40:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:41:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:41:39 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:42:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:42:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:43:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:43:39 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:43:42 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:43:42 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:44:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:44:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:45:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:45:39 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:46:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:46:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:47:40 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:47:40 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:48:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:48:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:49:40 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:49:40 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:50:23 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:50:23 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:51:41 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:51:41 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:52:24 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:52:24 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:53:41 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:53:41 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:54:24 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:54:24 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:55:42 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:55:42 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:56:25 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:56:25 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:57:42 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:57:42 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:58:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:58:26 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:59:42 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:59:42 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 18:59:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 18:59:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:00:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:00:26 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:01:43 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:01:43 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:02:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:02:26 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:03:43 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:03:43 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:04:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:04:26 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:05:43 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:05:43 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:06:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:06:26 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:07:43 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:07:43 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:08:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:08:26 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:09:43 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:09:43 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:10:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:10:26 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:11:44 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:11:44 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:12:27 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:12:27 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:13:44 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:13:44 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:14:27 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:14:27 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:15:44 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:15:44 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:15:47 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:15:47 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:16:27 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:16:27 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:17:44 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:17:44 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:18:28 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:18:28 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:19:44 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:19:44 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:20:28 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:20:28 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:21:44 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:21:44 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:22:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:22:29 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:23:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:23:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:24:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:24:29 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:25:45 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:25:45 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:26:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:26:29 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:27:47 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:27:47 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:28:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:28:29 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:29:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:29:46 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:30:30 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:30:30 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:31:47 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:31:47 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:31:49 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:31:49 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:32:31 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:32:31 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:33:47 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:33:47 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:34:31 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:34:31 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:35:47 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:35:47 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:36:31 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:36:31 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:37:48 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:37:48 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:38:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:38:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:39:48 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:39:48 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:40:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:40:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:41:49 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:41:49 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:42:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:42:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:43:49 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:43:49 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:44:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:44:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:45:49 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:45:49 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:46:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:46:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:47:49 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:47:49 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:47:53 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:47:53 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:48:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:48:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:49:49 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:49:49 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:50:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:50:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:51:50 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:51:50 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:52:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:52:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:53:50 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:53:50 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:54:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:54:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:55:50 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:55:50 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:56:32 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:56:32 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:57:53 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:57:53 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:58:33 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:58:33 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 19:59:52 UTC] WPC Frontend: Constructor called
[02-Jul-2025 19:59:52 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:00:33 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:00:33 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:01:52 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:01:52 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:02:34 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:02:34 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:03:52 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:03:52 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:03:56 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:03:56 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:04:34 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:04:34 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:05:52 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:05:52 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:06:35 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:06:35 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:07:53 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:07:53 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:08:35 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:08:35 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:09:53 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:09:53 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:10:35 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:10:35 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:10:36 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:10:36 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:10:37 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 20:10:37 UTC] WPC Debug - User ID: 1
[02-Jul-2025 20:10:37 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 20:10:38 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:10:38 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:10:54 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:10:54 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:10:54 UTC] WPC Upload - Function called
[02-Jul-2025 20:10:54 UTC] WPC Upload - POST nonce: f22da9c0f3
[02-Jul-2025 20:10:54 UTC] WPC Upload - Current user ID: 1
[02-Jul-2025 20:10:54 UTC] WPC Upload - Is user logged in: yes
[02-Jul-2025 20:10:54 UTC] WPC Upload - Nonce check result: VALID
[02-Jul-2025 20:10:54 UTC] WPC Upload - Nonce verified successfully
[02-Jul-2025 20:11:54 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:11:54 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:12:38 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:12:38 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:13:20 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:13:20 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:13:20 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 20:13:20 UTC] WPC Debug - User ID: 1
[02-Jul-2025 20:13:20 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 20:13:21 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:13:21 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:13:41 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:13:41 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:13:42 UTC] WPC Upload - Function called
[02-Jul-2025 20:13:42 UTC] WPC Upload - POST nonce: f22da9c0f3
[02-Jul-2025 20:13:42 UTC] WPC Upload - Current user ID: 1
[02-Jul-2025 20:13:42 UTC] WPC Upload - Is user logged in: yes
[02-Jul-2025 20:13:42 UTC] WPC Upload - Nonce check result: VALID
[02-Jul-2025 20:13:42 UTC] WPC Upload - Nonce verified successfully
[02-Jul-2025 20:13:42 UTC] WPC Upload - Processing file: PoC prints splash.png
[02-Jul-2025 20:13:42 UTC] WPC Upload - Field ID: 1
[02-Jul-2025 20:13:42 UTC] WPC Upload - Max size: 5 MB
[02-Jul-2025 20:13:42 UTC] WPC Upload - Image info: 2641x2569
[02-Jul-2025 20:13:42 UTC] WPC Upload - Starting file upload process
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Upload dir: Array
(
    [path] => /var/www/html/wp-content/uploads/2025/07
    [url] => http://pocprints.local/wp-content/uploads/2025/07
    [subdir] => /2025/07
    [basedir] => /var/www/html/wp-content/uploads
    [baseurl] => http://pocprints.local/wp-content/uploads
    [error] => 
)

[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Temp dir: /var/www/html/wp-content/uploads/wpc-temp
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Session ID: test_session_2025_07_02
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Session dir: /var/www/html/wp-content/uploads/wpc-temp/test_session_2025_07_02
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Creating temp dir: /var/www/html/wp-content/uploads/wpc-temp
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Temp dir created successfully
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Creating session dir: /var/www/html/wp-content/uploads/wpc-temp/test_session_2025_07_02
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Session dir created successfully
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Generated filename: field_1_1751487222_686592f60687a.png
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Full file path: /var/www/html/wp-content/uploads/wpc-temp/test_session_2025_07_02/field_1_1751487222_686592f60687a.png
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - Temp file: /tmp/phpkys6hh
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - File exists in temp: yes
[02-Jul-2025 20:13:42 UTC] WPC Upload Debug - File moved successfully to: /var/www/html/wp-content/uploads/wpc-temp/test_session_2025_07_02/field_1_1751487222_686592f60687a.png
[02-Jul-2025 20:13:42 UTC] WPC Upload - Upload successful: Array
(
    [filename] => field_1_1751487222_686592f60687a.png
    [url] => http://pocprints.local/wp-content/uploads/wpc-temp/test_session_2025_07_02/field_1_1751487222_686592f60687a.png
    [path] => /var/www/html/wp-content/uploads/wpc-temp/test_session_2025_07_02/field_1_1751487222_686592f60687a.png
    [size] => 653 KB
    [session_id] => test_session_2025_07_02
)

[02-Jul-2025 20:13:54 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:13:54 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 20:15:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 20:15:22 UTC] WPC Frontend: Constructor finished

[09-Oct-2024 00:41:27 UTC] PHP Warning:  Undefined array key 1 in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/class-plugin-optin.php on line 88
[09-Oct-2024 00:41:27 UTC] PHP Deprecated:  strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/class-plugin-optin.php on line 88
[09-Oct-2024 00:41:27 UTC] WordPress database error Table 'pocprints.wp_trp_dictionary_pt_br_' doesn't exist for query SELECT COUNT(id) FROM wp_trp_dictionary_pt_br_ WHERE translated != '' made by do_action('admin_init'), WP_Hook->do_action, WP_Hook->apply_filters, TRP_Plugin_Optin->process_optin_actions
[09-Oct-2024 00:41:29 UTC] PHP Warning:  Undefined array key "tp-add-on-seo-pack/tp-seo-pack.php" in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/advanced-settings/load-legacy-seo-pack.php on line 7
[09-Oct-2024 00:41:30 UTC] PHP Warning:  Undefined array key "tp-add-on-seo-pack/tp-seo-pack.php" in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/advanced-settings/load-legacy-seo-pack.php on line 7
[09-Oct-2024 00:51:08 UTC] PHP Warning:  Undefined array key "tp-add-on-seo-pack/tp-seo-pack.php" in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/advanced-settings/load-legacy-seo-pack.php on line 7
[09-Oct-2024 00:51:08 UTC] PHP Warning:  Undefined array key "tp-add-on-seo-pack/tp-seo-pack.php" in /var/www/html/wp-content/plugins/translatepress-multilingual/includes/advanced-settings/load-legacy-seo-pack.php on line 7
[09-Oct-2024 17:10:27 UTC] PHP Warning:  Undefined array key "old" in /home/<USER>/public_html/wp-content/plugins/wp-fail2ban/lib/site-health.php on line 619
[02-Jul-2025 01:46:33 UTC] PHP Fatal error:  Uncaught Error: Class "WPC_Product_Integration" not found in /var/www/html/wp-content/plugins/woocommerce-product-customizer/woocommerce-product-customizer.php:115
Stack trace:
#0 /var/www/html/wp-includes/class-wp-hook.php(324): WooCommerce_Product_Customizer->init_woocommerce_integration('')
#1 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#2 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /var/www/html/wp-settings.php(578): do_action('plugins_loaded')
#4 /var/www/html/wp-config.php(144): require_once('/var/www/html/w...')
#5 /var/www/html/wp-load.php(50): require_once('/var/www/html/w...')
#6 /var/www/html/wp-admin/admin.php(35): require_once('/var/www/html/w...')
#7 /var/www/html/wp-admin/plugins.php(10): require_once('/var/www/html/w...')
#8 {main}
  thrown in /var/www/html/wp-content/plugins/woocommerce-product-customizer/woocommerce-product-customizer.php on line 115
[02-Jul-2025 01:46:33 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>woocommerce</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 01:48:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 01:48:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 01:59:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:01:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:01:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:15:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:15:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:18:04 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:18:06 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:19:47 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:19:49 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:19:54 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:21:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:21:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:21:20 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:22:29 UTC] PHP Warning:  wp_version_check(): Ocorreu um erro inesperado. Algo pode estar errado com o WordPress.org ou com a configuração deste servidor. Se os problemas persistirem, busque ajuda no <a href="https://br.wordpress.org/support/forums/">fórum de suporte</a>. (O WordPress não conseguiu estabelecer uma conexão segura com o WordPress.org. Contate o administrador do servidor.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:22:35 UTC] PHP Warning:  wp_update_themes(): Ocorreu um erro inesperado. Algo pode estar errado com o WordPress.org ou com a configuração deste servidor. Se os problemas persistirem, busque ajuda no <a href="https://br.wordpress.org/support/forums/">fórum de suporte</a>. (O WordPress não conseguiu estabelecer uma conexão segura com o WordPress.org. Contate o administrador do servidor.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:22:58 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:23:00 UTC] PHP Warning:  foreach() argument must be of type array|object, string given in /var/www/html/wp-content/plugins/superfrete/api/Http/Request.php on line 75
[02-Jul-2025 02:23:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:23:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:23:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:23:49 UTC] WordPress database error You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'FOREIGN KEY (customization_id) REFERENCES wp_wpc_customizations(id) ON DELETE...' at line 1 for query ALTER TABLE wp_wpc_customization_fields ADD COLUMN FOREIGN KEY (customization_id) REFERENCES wp_wpc_customizations(id) ON DELETE CASCADE made by activate_plugin, do_action('activate_woocommerce-product-customizer/woocommerce-product-customizer.php'), WP_Hook->do_action, WP_Hook->apply_filters, WooCommerce_Product_Customizer->activate, WPC_Database::create_tables, dbDelta
[02-Jul-2025 02:24:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:24:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:24:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:17 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:19 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:20 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:21 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:24:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 02:24:34 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:35 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:36 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:37 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:38 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:39 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:40 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:41 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:46 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:24:50 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:28:10 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:28:10 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:28:10 UTC] PHP Fatal error:  Uncaught Error: Class "WPC_Product_Integration" not found in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php:244
Stack trace:
#0 /var/www/html/wp-includes/class-wp-hook.php(324): WPC_Frontend->enqueue_frontend_scripts('')
#1 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#2 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /var/www/html/wp-includes/script-loader.php(2299): do_action('wp_enqueue_scri...')
#4 /var/www/html/wp-includes/class-wp-hook.php(324): wp_enqueue_scripts('')
#5 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#6 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#7 /var/www/html/wp-includes/general-template.php(3192): do_action('wp_head')
#8 /var/www/html/wp-content/themes/storefront/header.php(18): wp_head()
#9 /var/www/html/wp-includes/template.php(810): require_once('/var/www/html/w...')
#10 /var/www/html/wp-includes/template.php(745): load_template('/var/www/html/w...', true, Array)
#11 /var/www/html/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)
#12 /var/www/html/wp-content/plugins/woocommerce/templates/single-product.php(22): get_header('shop')
#13 /var/www/html/wp-includes/template-loader.php(106): include('/var/www/html/w...')
#14 /var/www/html/wp-blog-header.php(19): require_once('/var/www/html/w...')
#15 /var/www/html/index.php(17): require('/var/www/html/w...')
#16 {main}
  thrown in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php on line 244
[02-Jul-2025 02:28:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:28:11 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:31:43 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:31:43 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:31:44 UTC] PHP Fatal error:  Uncaught Error: Call to a member function get_id() on string in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php:242
Stack trace:
#0 /var/www/html/wp-includes/class-wp-hook.php(324): WPC_Frontend->enqueue_frontend_scripts('')
#1 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#2 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /var/www/html/wp-includes/script-loader.php(2299): do_action('wp_enqueue_scri...')
#4 /var/www/html/wp-includes/class-wp-hook.php(324): wp_enqueue_scripts('')
#5 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#6 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#7 /var/www/html/wp-includes/general-template.php(3192): do_action('wp_head')
#8 /var/www/html/wp-content/themes/storefront/header.php(18): wp_head()
#9 /var/www/html/wp-includes/template.php(810): require_once('/var/www/html/w...')
#10 /var/www/html/wp-includes/template.php(745): load_template('/var/www/html/w...', true, Array)
#11 /var/www/html/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)
#12 /var/www/html/wp-content/plugins/woocommerce/templates/single-product.php(22): get_header('shop')
#13 /var/www/html/wp-includes/template-loader.php(106): include('/var/www/html/w...')
#14 /var/www/html/wp-blog-header.php(19): require_once('/var/www/html/w...')
#15 /var/www/html/index.php(17): require('/var/www/html/w...')
#16 {main}
  thrown in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php on line 242
[02-Jul-2025 02:31:44 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:31:44 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:33:25 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:33:25 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:33:26 UTC] PHP Fatal error:  Uncaught Error: Call to a member function get_id() on string in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php:242
Stack trace:
#0 /var/www/html/wp-includes/class-wp-hook.php(324): WPC_Frontend->enqueue_frontend_scripts('')
#1 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#2 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /var/www/html/wp-includes/script-loader.php(2299): do_action('wp_enqueue_scri...')
#4 /var/www/html/wp-includes/class-wp-hook.php(324): wp_enqueue_scripts('')
#5 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#6 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#7 /var/www/html/wp-includes/general-template.php(3192): do_action('wp_head')
#8 /var/www/html/wp-content/themes/storefront/header.php(18): wp_head()
#9 /var/www/html/wp-includes/template.php(810): require_once('/var/www/html/w...')
#10 /var/www/html/wp-includes/template.php(745): load_template('/var/www/html/w...', true, Array)
#11 /var/www/html/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)
#12 /var/www/html/wp-content/plugins/woocommerce/templates/single-product.php(22): get_header('shop')
#13 /var/www/html/wp-includes/template-loader.php(106): include('/var/www/html/w...')
#14 /var/www/html/wp-blog-header.php(19): require_once('/var/www/html/w...')
#15 /var/www/html/index.php(17): require('/var/www/html/w...')
#16 {main}
  thrown in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php on line 242
[02-Jul-2025 02:33:26 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:33:26 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:33:29 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:33:29 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:33:29 UTC] PHP Fatal error:  Uncaught Error: Call to a member function get_id() on string in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php:242
Stack trace:
#0 /var/www/html/wp-includes/class-wp-hook.php(324): WPC_Frontend->enqueue_frontend_scripts('')
#1 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#2 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /var/www/html/wp-includes/script-loader.php(2299): do_action('wp_enqueue_scri...')
#4 /var/www/html/wp-includes/class-wp-hook.php(324): wp_enqueue_scripts('')
#5 /var/www/html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)
#6 /var/www/html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#7 /var/www/html/wp-includes/general-template.php(3192): do_action('wp_head')
#8 /var/www/html/wp-content/themes/storefront/header.php(18): wp_head()
#9 /var/www/html/wp-includes/template.php(810): require_once('/var/www/html/w...')
#10 /var/www/html/wp-includes/template.php(745): load_template('/var/www/html/w...', true, Array)
#11 /var/www/html/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)
#12 /var/www/html/wp-content/plugins/woocommerce/templates/single-product.php(22): get_header('shop')
#13 /var/www/html/wp-includes/template-loader.php(106): include('/var/www/html/w...')
#14 /var/www/html/wp-blog-header.php(19): require_once('/var/www/html/w...')
#15 /var/www/html/index.php(17): require('/var/www/html/w...')
#16 {main}
  thrown in /var/www/html/wp-content/plugins/woocommerce-product-customizer/includes/class-wpc-frontend.php on line 242
[02-Jul-2025 02:33:30 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:33:30 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:37:38 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:37:38 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 02:37:39 UTC] WPC Frontend: display_customization_fields called
[02-Jul-2025 02:37:39 UTC] WPC Frontend: Product ID: 10150
[02-Jul-2025 02:37:39 UTC] WPC Frontend: Enable customization meta: yes
[02-Jul-2025 02:37:39 UTC] WPC Frontend: Product 10150 is customizable - proceeding
[02-Jul-2025 02:37:41 UTC] WPC Frontend: Constructor called
[02-Jul-2025 02:37:41 UTC] WPC Frontend: init_hooks() called
[02-Jul-2025 13:54:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 13:55:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 13:56:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 13:56:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 13:56:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 15:00:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 15:00:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-sku-generator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Jul-2025 15:57:17 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 15:57:17 UTC] WPC Debug - User ID: 1
[02-Jul-2025 15:57:17 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 16:02:09 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 16:02:09 UTC] WPC Debug - User ID: 1
[02-Jul-2025 16:02:09 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 16:10:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:10:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:10:43 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:10:43 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:10:44 UTC] WPC Frontend: test_basic nopriv called
[02-Jul-2025 16:11:10 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:11:10 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:11:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:11:11 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:11:40 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:11:40 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:11:40 UTC] WPC Test Upload - Function called
[02-Jul-2025 16:11:40 UTC] WPC Test Upload - POST nonce: NOT SET
[02-Jul-2025 16:11:50 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:11:50 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:11:50 UTC] WPC Test Upload - Function called
[02-Jul-2025 16:11:50 UTC] WPC Test Upload - POST nonce: f22da9c0f3
[02-Jul-2025 16:12:14 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:12:14 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:12:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:12:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:12:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:12:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:13:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:13:11 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:13:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:13:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:15:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:15:11 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:15:34 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:15:34 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:15:50 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:15:50 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:18 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:16:18 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:19 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 16:16:19 UTC] WPC Debug - User ID: 1
[02-Jul-2025 16:16:19 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 16:16:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:16:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:25 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:16:25 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:25 UTC] WPC Test Upload - Function called
[02-Jul-2025 16:16:25 UTC] WPC Test Upload - POST nonce: f22da9c0f3
[02-Jul-2025 16:16:30 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:16:30 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:35 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:16:35 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:16:35 UTC] WPC Upload - Function called
[02-Jul-2025 16:16:35 UTC] WPC Upload - POST nonce: f22da9c0f3
[02-Jul-2025 16:16:35 UTC] WPC Upload - Current user ID: 1
[02-Jul-2025 16:16:35 UTC] WPC Upload - Is user logged in: yes
[02-Jul-2025 16:16:35 UTC] WPC Upload - Nonce check result: VALID
[02-Jul-2025 16:16:35 UTC] WPC Upload - Nonce verified successfully
[02-Jul-2025 16:17:11 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:17:11 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:18:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:18:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:18:24 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:18:24 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:19:12 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:19:12 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:20:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:20:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:21:12 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:21:12 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:21:15 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:21:15 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:21:15 UTC] WPC Debug - Creating nonce: f22da9c0f3
[02-Jul-2025 16:21:15 UTC] WPC Debug - User ID: 1
[02-Jul-2025 16:21:15 UTC] WPC Debug - Is user logged in: yes
[02-Jul-2025 16:21:16 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:21:16 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:21:22 UTC] WPC Frontend: Constructor called
[02-Jul-2025 16:21:22 UTC] WPC Frontend: Constructor finished
[02-Jul-2025 16:21:22 UTC] WPC Upload - Function called
[02-Jul-2025 16:21:22 UTC] WPC Upload - POST nonce: f22da9c0f3
[02-Jul-2025 16:21:22 UTC] WPC Upload - Current user ID: 1
[02-Jul-2025 16:21:22 UTC] WPC Upload - Is user logged in: yes
[02-Jul-2025 16:21:22 UTC] WPC Upload - Nonce check result: VALID
[02-Jul-2025 16:21:22 UTC] WPC Upload - Nonce verified successfully

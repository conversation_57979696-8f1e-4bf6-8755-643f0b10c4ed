<?php
/**
 * Plugin Name: WooCommerce Product Customizer
 * Plugin URI: https://pocprints.com.br
 * Description: Plugin para permitir que clientes anexem arquivos para produtos customizados no WooCommerce.
 * Version: 2.1.2
 * Author: PoC Prints
 * Author URI: https://pocprints.com.br
 * Text Domain: woocommerce-product-customizer
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 9.8
 * Woo: 9.8.5:tested
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if WooCommerce is active
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins'))) && !is_plugin_active_for_network('woocommerce/woocommerce.php')) {
    return;
}

// Define plugin constants
define('WPC_PLUGIN_FILE', __FILE__);
define('WPC_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('WPC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WPC_VERSION', '2.1.2');
define('WPC_TEXT_DOMAIN', 'woocommerce-product-customizer');

/**
 * Main WooCommerce Product Customizer Class
 */
class WooCommerce_Product_Customizer {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        add_action('plugins_loaded', array($this, 'init_woocommerce_integration'));
        add_action('before_woocommerce_init', array($this, 'declare_compatibility'));

        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Load required files
        $this->load_dependencies();

        // Initialize admin
        if (is_admin()) {
            $this->init_admin();
        }

        // Initialize frontend - load for both frontend display AND AJAX requests
        new WPC_Frontend();
    }

    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once WPC_PLUGIN_PATH . 'includes/class-wpc-database.php';
        require_once WPC_PLUGIN_PATH . 'includes/class-wpc-admin.php';
        require_once WPC_PLUGIN_PATH . 'includes/class-wpc-customization.php';
        require_once WPC_PLUGIN_PATH . 'includes/class-wpc-frontend.php';
        // WPC_Product_Integration is loaded separately in init_woocommerce_integration()
    }

    /**
     * Initialize admin functionality
     */
    private function init_admin() {
        new WPC_Admin();
    }

    /**
     * Initialize WooCommerce integration
     */
    public function init_woocommerce_integration() {
        // Only initialize product integration if WooCommerce is loaded
        if (class_exists('WooCommerce') && is_admin()) {
            // Load the product integration class
            require_once WPC_PLUGIN_PATH . 'includes/class-wpc-product-integration.php';
            new WPC_Product_Integration();
        }
    }

    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            WPC_TEXT_DOMAIN,
            false,
            dirname(plugin_basename(__FILE__)) . '/languages/'
        );
    }

    /**
     * Declare compatibility with WooCommerce features
     */
    public function declare_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('cart_checkout_blocks', __FILE__, true);
        }
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Load database class
        require_once WPC_PLUGIN_PATH . 'includes/class-wpc-database.php';

        // Create database tables
        WPC_Database::create_tables();
        
        // Set default options
        add_option('wpc_version', WPC_VERSION);
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
}

/**
 * Initialize the plugin
 */
function wpc_init() {
    return WooCommerce_Product_Customizer::get_instance();
}

// Start the plugin
wpc_init();

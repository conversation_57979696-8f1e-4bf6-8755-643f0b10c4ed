<?php
/**
 * Debug script to check and fix WordPress URLs
 * Temporary file for debugging
 */

// Include WordPress
require_once('../../../wp-load.php');

echo "=== WordPress URL Debug ===\n";
echo "Home URL: " . get_option('home') . "\n";
echo "Site URL: " . get_option('siteurl') . "\n";
echo "Admin URL: " . admin_url('admin-ajax.php') . "\n";
echo "Current URL: " . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'N/A') . "\n";
echo "Server Name: " . (isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'N/A') . "\n";

// Check if we need to update URLs
$current_host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
$home_url = get_option('home');
$site_url = get_option('siteurl');

if ($current_host === 'localhost' && strpos($home_url, 'pocprints.local') !== false) {
    echo "\n=== URL Mismatch Detected ===\n";
    echo "WordPress is configured for pocprints.local but accessed via localhost\n";
    echo "This causes AJAX 400 errors\n";
    
    echo "\nTo fix, you can either:\n";
    echo "1. Access via http://pocprints.local\n";
    echo "2. Update WordPress URLs to localhost\n";
    
    // Uncomment the lines below to automatically fix URLs to localhost
    /*
    update_option('home', 'http://localhost');
    update_option('siteurl', 'http://localhost');
    echo "\nURLs updated to localhost!\n";
    */
}

echo "\n=== End Debug ===\n";
?>

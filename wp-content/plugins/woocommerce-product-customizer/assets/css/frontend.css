/* WooCommerce Product Customizer Frontend Styles */

.wpc-product-customization {
    margin: 15px 0;
    padding: 0;
    border: none;
    background: transparent;
}



.wpc-required {
    color: #d63638;
    font-weight: bold;
}

.wpc-price {
    color: #0073aa;
    font-size: 16px;
    font-weight: 500;
}

.wpc-instructions {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-left: 4px solid #0073aa;
    border-radius: 4px;
}

.wpc-instructions p {
    margin: 0;
    color: #666;
    line-height: 1.5;
}

.wpc-customization-group {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #ddd;
}

.wpc-field-wrapper {
    margin-bottom: 20px;
}

.wpc-field-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.wpc-field-info {
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

/* Text Fields */
.wpc-text-field {
    width: 100%;
    min-height: 60px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.wpc-text-field:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.wpc-char-counter {
    text-align: right;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.wpc-char-counter.wpc-warning {
    color: #dba617;
}

.wpc-char-counter.wpc-error {
    color: #d63638;
}

/* Image Upload Fields */
.wpc-image-field {
    position: relative;
}

.wpc-upload-area {
    position: relative;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 120px;
    max-height: 120px;
    width: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    overflow: hidden;
}

.wpc-upload-area:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.wpc-upload-area.has-image {
    border: none;
    background: transparent;
    padding: 0;
}

.wpc-upload-area.has-image:hover {
    background: transparent;
}

.wpc-upload-area.wpc-dragover {
    border-color: #0073aa;
    background: #e6f3ff;
    transform: scale(1.02);
}

.wpc-upload-content {
    pointer-events: none;
}

.wpc-upload-icon {
    margin-bottom: 8px;
    color: #666;
}

.wpc-upload-icon svg {
    width: 32px;
    height: 32px;
}

/* Upload Instructions - moved outside upload area */
.wpc-upload-instructions {
    margin-top: 8px;
    text-align: left;
}

.wpc-upload-text {
    margin: 0 0 4px 0;
    font-size: 13px;
    font-weight: 500;
    color: #333;
    line-height: 1.3;
}

.wpc-upload-requirements {
    margin: 0;
    font-size: 11px;
    color: #666;
    line-height: 1.2;
}

.wpc-file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Image Preview */
.wpc-image-preview {
    margin-top: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #fff;
    max-width: 100%;
    overflow: hidden;
}

.wpc-image-container {
    position: relative;
    display: inline-block;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
}

.wpc-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 6px;
    transition: transform 0.2s ease;
}

.wpc-thumbnail:hover {
    transform: scale(1.05);
}

.wpc-image-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.wpc-image-container:hover .wpc-image-actions {
    opacity: 1;
}

.wpc-remove-image {
    background: rgba(214, 54, 56, 0.9);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.wpc-remove-image:hover {
    background: rgba(179, 45, 46, 0.95);
    transform: scale(1.1);
}

.wpc-image-info {
    margin-top: 10px;
    font-size: 12px;
    color: #666;
}

.wpc-filename {
    display: block;
    font-weight: 500;
    margin-bottom: 2px;
}

.wpc-filesize {
    color: #999;
}

/* Upload Progress */
.wpc-upload-progress {
    margin-top: 10px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    height: 6px;
}

.wpc-upload-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    width: 0%;
    transition: width 0.3s ease;
}

.wpc-upload-status {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
    text-align: center;
}

/* Validation Messages */
.wpc-validation-messages {
    margin-top: 15px;
}

.wpc-error-message {
    padding: 10px 15px;
    background: #ffeaea;
    border: 1px solid #d63638;
    border-radius: 4px;
    color: #d63638;
    font-size: 14px;
    margin-bottom: 10px;
}

.wpc-success-message {
    padding: 10px 15px;
    background: #eafaf1;
    border: 1px solid #00a32a;
    border-radius: 4px;
    color: #00a32a;
    font-size: 14px;
    margin-bottom: 10px;
}

/* Loading States */
.wpc-loading {
    opacity: 0.6;
    pointer-events: none;
}

.wpc-loading .wpc-upload-area {
    background: #f0f0f0;
    border-color: #ccc;
}

.wpc-loading .wpc-upload-text::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    border: 2px solid #ccc;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: wpc-spin 1s linear infinite;
}

@keyframes wpc-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .wpc-product-customization {
        margin: 15px 0;
        padding: 15px;
    }
    

    
    .wpc-customization-group {
        padding: 15px;
    }
    
    .wpc-upload-area {
        padding: 15px 10px;
        max-width: 150px;
    }

    .wpc-upload-icon svg {
        width: 36px;
        height: 36px;
    }
    
    .wpc-upload-text {
        font-size: 14px;
    }
    
    .wpc-image-container {
        max-width: 150px;
    }
}

@media screen and (max-width: 480px) {
    .wpc-product-customization {
        margin: 10px 0;
        padding: 10px;
    }
    
    .wpc-customization-group {
        padding: 10px;
    }
    
    .wpc-upload-area {
        padding: 12px 8px;
        max-width: 100px;
    }
    
    .wpc-text-field {
        min-height: 60px;
        padding: 10px;
    }
}

jQuery(document).ready(function($) {

    // Toggle customization options when enable checkbox is changed
    $('#wpc_enable_customization').on('change', function() {
        if ($(this).is(':checked')) {
            $('#wpc_customization_options').slideDown();
        } else {
            $('#wpc_customization_options').slideUp();
        }
    });

    // Handle customization selection from dropdown
    $('#wpc_customization_select').on('change', function() {
        const customizationId = $(this).val();
        const customizationName = $(this).find('option:selected').data('name');

        if (!customizationId) return;

        // Check if already selected
        if ($('#wpc_selected_items').find(`[data-id="${customizationId}"]`).length > 0) {
            alert('Esta personalização já foi selecionada.');
            $(this).val('');
            return;
        }

        // Get customization data
        const customizationData = JSON.parse($('#wpc_customization_data_' + customizationId).text());

        // Create selected item HTML
        const itemHtml = createSelectedItemHtml(customizationData);

        // Add to selected items
        $('#wpc_selected_items').append(itemHtml);

        // Hide "no customizations" message
        $('#wpc_no_customizations').hide();

        // Reset select
        $(this).val('');

        // Update form validation
        updateFormValidation();
    });
    
    // Remove selected customization
    $(document).on('click', '.wpc-remove-item', function(e) {
        e.preventDefault();

        const item = $(this).closest('.wpc-selected-item');
        item.fadeOut(300, function() {
            $(this).remove();

            // Show "no customizations" message if none left
            if ($('#wpc_selected_items .wpc-selected-item').length === 0) {
                $('#wpc_no_customizations').show();
            }

            updateFormValidation();
        });
    });

    // Validate customization price
    $('#wpc_customization_price').on('input', function() {
        const value = parseFloat($(this).val());

        if (value < 0) {
            $(this).val(0);
        }

        // Format to 2 decimal places
        if (!isNaN(value)) {
            $(this).val(value.toFixed(2));
        }
    });

    // Auto-check enable customization when any customization is selected
    function updateFormValidation() {
        const hasCustomizations = $('#wpc_selected_items .wpc-selected-item').length > 0;

        if (hasCustomizations && !$('#wpc_enable_customization').is(':checked')) {
            $('#wpc_enable_customization').prop('checked', true).trigger('change');
        }
    }
    
    // Create HTML for selected customization item
    function createSelectedItemHtml(customizationData) {
        let fieldsHtml = '';

        if (customizationData.fields && customizationData.fields.length > 0) {
            fieldsHtml = '<ul>';
            customizationData.fields.forEach(function(field) {
                fieldsHtml += `<li><strong>${field.label}</strong> (${field.type.charAt(0).toUpperCase() + field.type.slice(1)} - Máx: ${field.max_size})</li>`;
            });
            fieldsHtml += '</ul>';
        } else {
            fieldsHtml = '<p class="description">Nenhum campo configurado.</p>';
        }

        return `
            <div class="wpc-selected-item" data-id="${customizationData.id}">
                <input type="hidden" name="wpc_customizations[]" value="${customizationData.id}">
                <div class="wpc-item-header">
                    <strong>${customizationData.name}</strong>
                    <button type="button" class="wpc-remove-item button-link-delete">Remover</button>
                </div>
                <div class="wpc-item-fields">
                    ${fieldsHtml}
                </div>
            </div>
        `;
    }

    // Validate form before submit
    $('form#post').on('submit', function(e) {
        const enableCustomization = $('#wpc_enable_customization').is(':checked');
        const selectedCustomizations = $('#wpc_selected_items .wpc-selected-item').length;

        if (enableCustomization && selectedCustomizations === 0) {
            e.preventDefault();
            alert('Por favor, selecione pelo menos uma personalização ou desabilite a personalização para este produto.');

            // Focus on the customization tab
            $('.wc-tabs li a[href="#wpc_customization_product_data"]').trigger('click');

            return false;
        }

        return true;
    });
    
    // Character counter for customization name
    $('#customization_name').on('input', function() {
        const currentLength = $(this).val().length;
        const maxLength = 34;

        // Remove existing counter
        $(this).next('.character-counter').remove();

        // Add counter
        const counter = $(`<span class="character-counter">${currentLength}/${maxLength}</span>`);
        $(this).after(counter);

        // Style based on length
        if (currentLength > maxLength) {
            counter.css('color', '#d63638');
            $(this).css('border-color', '#d63638');
        } else if (currentLength > maxLength * 0.8) {
            counter.css('color', '#dba617');
            $(this).css('border-color', '#dba617');
        } else {
            counter.css('color', '#00a32a');
            $(this).css('border-color', '');
        }
    });

    // Initialize form validation
    updateFormValidation();

    // Media uploader for preview images
    let mediaUploader;

    $(document).on('click', '.wpc-upload-btn', function(e) {
        e.preventDefault();

        const button = $(this);
        const targetField = button.data('target');

        // Create the media frame
        mediaUploader = wp.media({
            title: 'Selecionar Imagem para Pré-visualização',
            button: {
                text: 'Usar esta imagem'
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });

        // When an image is selected, run a callback
        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();

            // Set the URL in the input field
            $('#' + targetField).val(attachment.url);

            // Show preview
            const previewContainer = $('#' + targetField + '_preview');
            previewContainer.find('img').attr('src', attachment.url);
            previewContainer.show();
        });

        // Open the media frame
        mediaUploader.open();
    });

    // Remove image functionality
    $(document).on('click', '.wpc-remove-image', function(e) {
        e.preventDefault();

        const button = $(this);
        const targetField = button.data('target');

        // Clear the input field
        $('#' + targetField).val('');

        // Hide preview
        $('#' + targetField + '_preview').hide();
    });

    // Preview area helper
    $('#wpc_preview_area').on('blur', function() {
        const value = $(this).val().trim();
        if (value && !value.match(/^\d+,\d+,\d+,\d+$/)) {
            alert('Formato inválido. Use: x,y,largura,altura (ex: 100,50,300,200)');
            $(this).focus();
        }
    });

    // Show/hide preview fields based on enable checkbox
    $('#wpc_enable_preview').on('change', function() {
        const previewFields = $('.wpc-preview-fields');
        if ($(this).is(':checked')) {
            previewFields.find('.wpc-field-group').not(':last').slideDown();
        } else {
            previewFields.find('.wpc-field-group').not(':last').slideUp();
        }
    });

    // Initialize preview fields visibility
    if (!$('#wpc_enable_preview').is(':checked')) {
        $('.wpc-preview-fields .wpc-field-group').not(':last').hide();
    }

});

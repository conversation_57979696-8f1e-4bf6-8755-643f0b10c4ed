jQuery(document).ready(function($) {

    // Initialize customization fields
    initCustomizationFields();

    function initCustomizationFields() {
        // Initialize text fields
        initTextFields();

        // Initialize image upload fields
        initImageFields();

        // Validate form before add to cart
        validateFormOnSubmit();

        // Initialize add to cart button control
        initAddToCartControl();

        // Initialize image persistence system
        initImagePersistence();

        // Initial validation check
        validateAllFields();

        // Initialize cart restoration
        initCartRestoration();

        // Handle cart restoration parameter
        handleCartRestoration();

        // Force cart link modification via JavaScript (fallback solution)
        if ($('body').hasClass('woocommerce-cart') || window.location.pathname.includes('cart')) {
            forceCartLinkModification();
        }

        // Disable HTML5 validation on WooCommerce forms to prevent conflicts
        $('form.cart, form.variations_form').attr('novalidate', 'novalidate');
    }
    
    // Text Fields Functionality
    function initTextFields() {
        $('.wpc-text-field').each(function() {
            const field = $(this);
            const counter = field.siblings('.wpc-char-counter');
            const maxLength = parseInt(field.attr('maxlength'));
            
            // Update character counter
            field.on('input', function() {
                const currentLength = $(this).val().length;
                const current = counter.find('.wpc-current');
                
                current.text(currentLength);
                
                // Update counter styling
                counter.removeClass('wpc-warning wpc-error');
                if (currentLength > maxLength * 0.9) {
                    counter.addClass('wpc-warning');
                }
                if (currentLength >= maxLength) {
                    counter.addClass('wpc-error');
                }
                
                // Validate field
                validateTextField(field);

                // Check all fields after change
                validateAllFields();
            });

            // Initial validation
            validateTextField(field);
        });
    }
    
    // Image Fields Functionality
    function initImageFields() {
        $('.wpc-image-field').each(function() {
            const imageField = $(this);
            const uploadArea = imageField.find('.wpc-upload-area');
            const fileInput = imageField.find('.wpc-file-input');
            const preview = imageField.find('.wpc-image-preview');
            const maxSize = parseFloat(imageField.data('max-size'));
            
            // Click to upload
            uploadArea.on('click', function() {
                fileInput.click();
            });
            
            // File input change
            fileInput.on('change', function() {
                const file = this.files[0];
                if (file) {
                    handleFileUpload(file, imageField, maxSize);
                }
            });
            
            // Drag and drop functionality
            uploadArea.on('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('wpc-dragover');
            });
            
            uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('wpc-dragover');
            });
            
            uploadArea.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('wpc-dragover');
                
                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0], imageField, maxSize);
                }
            });
            
            // Remove image functionality
            imageField.on('click', '.wpc-remove-image', function(e) {
                e.preventDefault();
                removeImage(imageField);

                // Check all fields after removal
                validateAllFields();
            });
        });
    }
    
    // Handle file upload
    function handleFileUpload(file, imageField, maxSize) {
        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
        if (!allowedTypes.includes(file.type)) {
            showError(imageField, wpc_frontend.strings.invalid_format);
            return;
        }
        
        // Validate file size
        const maxSizeBytes = maxSize * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            showError(imageField, wpc_frontend.strings.file_too_large + ' ' + maxSize + ' MB');
            return;
        }
        
        // Show loading state
        imageField.addClass('wpc-loading');
        showUploadProgress(imageField, 0);
        
        // Create FormData
        const formData = new FormData();
        formData.append('file', file);
        formData.append('field_id', getFieldId(imageField));
        formData.append('max_size', maxSize);
        formData.append('action', 'wpc_upload_file');
        formData.append('nonce', wpc_frontend.nonce);


        console.log('- AJAX URL:', wpc_frontend.ajax_url);
        
        // Upload file via AJAX
        $.ajax({
            url: wpc_frontend.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,

            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        showUploadProgress(imageField, percentComplete);
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                imageField.removeClass('wpc-loading');
                hideUploadProgress(imageField);

                if (response.success) {
                    showImagePreview(imageField, response.data);
                    clearError(imageField);

                    // Trigger image uploaded event for persistence
                    const fieldId = imageField.find('input[type="file"]').attr('name').replace('wpc_field_', '');
                    $(document).trigger('wpc_image_uploaded', [fieldId, response.data]);

                    // Check all fields after successful upload
                    validateAllFields();
                } else {
                    showError(imageField, response.data.message);

                    // Check all fields after failed upload
                    validateAllFields();
                }
            },
            error: function(xhr, status, error) {
                imageField.removeClass('wpc-loading');
                hideUploadProgress(imageField);

                let errorMessage = wpc_frontend.strings.upload_error;
                if (xhr.status === 400) {
                    errorMessage = 'Erro 400: Requisição inválida. Verifique o arquivo selecionado.';
                } else if (xhr.status === 413) {
                    errorMessage = 'Erro 413: Arquivo muito grande para o servidor.';
                } else if (xhr.status === 500) {
                    errorMessage = 'Erro 500: Erro interno do servidor.';
                }

                showError(imageField, errorMessage);

                // Check all fields after error
                validateAllFields();
            }
        });
    }
    
    // Show image preview
    function showImagePreview(imageField, fileData) {
        const preview = imageField.find('.wpc-image-preview');
        const uploadArea = imageField.find('.wpc-upload-area');
        const thumbnail = preview.find('.wpc-thumbnail');
        const filename = preview.find('.wpc-filename');
        const filesize = preview.find('.wpc-filesize');
        const hiddenInput = imageField.find('.wpc-uploaded-file');

        // Set image data
        thumbnail.attr('src', fileData.url);
        filename.text(fileData.filename);
        filesize.text(fileData.size);
        hiddenInput.val(fileData.filename);

        // Hide upload area and show preview
        uploadArea.hide();
        preview.show();

        // Mark field as valid
        imageField.removeClass('wpc-invalid').addClass('wpc-valid');

        // Show preview button if preview is enabled
        const previewBtn = imageField.find('.wpc-preview-btn');
        if (previewBtn.length > 0) {
            previewBtn.show();
            previewBtn.data('image-url', fileData.url);
        }
    }
    
    // Remove image
    function removeImage(imageField) {
        const filename = imageField.find('.wpc-uploaded-file').val();
        
        if (filename) {
            // Delete file from server
            $.ajax({
                url: wpc_frontend.ajax_url,
                type: 'POST',
                data: {
                    action: 'wpc_delete_file',
                    filename: filename,
                    nonce: wpc_frontend.nonce
                }
            });
        }
        
        // Reset field
        const uploadArea = imageField.find('.wpc-upload-area');
        const preview = imageField.find('.wpc-image-preview');
        const fileInput = imageField.find('.wpc-file-input');
        const hiddenInput = imageField.find('.wpc-uploaded-file');

        // Hide preview and show upload area
        preview.hide();
        uploadArea.show();
        fileInput.val('');
        hiddenInput.val('');

        // Mark field as invalid
        imageField.removeClass('wpc-valid').addClass('wpc-invalid');
        
        clearError(imageField);
    }
    
    // Show upload progress
    function showUploadProgress(imageField, percent) {
        let progressContainer = imageField.find('.wpc-upload-progress');
        
        if (progressContainer.length === 0) {
            progressContainer = $(`
                <div class="wpc-upload-progress">
                    <div class="wpc-upload-progress-bar"></div>
                </div>
                <div class="wpc-upload-status">${wpc_frontend.strings.uploading}</div>
            `);
            imageField.find('.wpc-upload-area').after(progressContainer);
        }
        
        const progressBar = progressContainer.find('.wpc-upload-progress-bar');
        progressBar.css('width', percent + '%');
    }
    
    // Hide upload progress
    function hideUploadProgress(imageField) {
        imageField.find('.wpc-upload-progress, .wpc-upload-status').remove();
    }
    
    // Validate text field
    function validateTextField(field) {
        const value = field.val().trim();
        const isRequired = field.prop('required');
        
        if (isRequired && value === '') {
            field.removeClass('wpc-valid').addClass('wpc-invalid');
            return false;
        } else {
            field.removeClass('wpc-invalid').addClass('wpc-valid');
            return true;
        }
    }
    
    // Show error message
    function showError(field, message) {
        clearError(field);
        
        const errorDiv = $('<div class="wpc-error-message">' + message + '</div>');
        field.after(errorDiv);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            errorDiv.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }
    
    // Clear error message
    function clearError(field) {
        field.siblings('.wpc-error-message').remove();
    }
    
    // Get field ID from image field
    function getFieldId(imageField) {
        const fileInput = imageField.find('.wpc-file-input');
        const fieldId = fileInput.attr('id').replace('wpc_field_', '');
        return fieldId;
    }
    
    // Validate form before add to cart
    function validateFormOnSubmit() {
        $('form.cart').on('submit', function(e) {
            const customizationContainer = $('#wpc-customization-fields');
            
            if (customizationContainer.length === 0) {
                return true; // No customization fields
            }
            
            let isValid = true;
            const errors = [];
            
            // Validate text fields
            customizationContainer.find('.wpc-text-field').each(function() {
                if (!validateTextField($(this))) {
                    isValid = false;
                    errors.push(wpc_frontend.strings.required_field);
                }
            });
            
            // Validate image fields
            customizationContainer.find('.wpc-image-field').each(function() {
                const hiddenInput = $(this).find('.wpc-uploaded-file');
                if (hiddenInput.val() === '') {
                    isValid = false;
                    $(this).removeClass('wpc-valid').addClass('wpc-invalid');
                    errors.push(wpc_frontend.strings.required_field);
                }
            });
            
            // Show validation messages
            const messagesContainer = customizationContainer.find('.wpc-validation-messages');
            messagesContainer.empty();
            
            if (!isValid) {
                e.preventDefault();
                
                const uniqueErrors = [...new Set(errors)];
                uniqueErrors.forEach(function(error) {
                    messagesContainer.append('<div class="wpc-error-message">' + error + '</div>');
                });
                
                // Scroll to first error
                $('html, body').animate({
                    scrollTop: customizationContainer.offset().top - 100
                }, 500);
                
                return false;
            }
            
            return true;
        });
    }

    // Add to Cart Button Control
    function initAddToCartControl() {
        // Check if customization is required
        const customizationContainer = $('#wpc-customization-fields');
        if (customizationContainer.length === 0) {
            return; // No customization fields, nothing to control
        }

        // Add CSS class to identify controlled button
        const addToCartButton = $('button[name="add-to-cart"], .single_add_to_cart_button');
        addToCartButton.addClass('wpc-controlled-button');

        // Store original button text
        if (!addToCartButton.data('original-text')) {
            addToCartButton.data('original-text', addToCartButton.text());
        }
    }

    // Validate all customization fields
    function validateAllFields() {
        // Prevent infinite recursion
        if (window.wpcValidating) {
            return true;
        }
        window.wpcValidating = true;

        const customizationContainer = $('#wpc-customization-fields');
        if (customizationContainer.length === 0) {
            window.wpcValidating = false;
            return true; // No customization fields
        }

        let allValid = true;
        let hasRequiredFields = false;

        // Check text fields
        customizationContainer.find('.wpc-text-field').each(function() {
            const field = $(this);
            const value = field.val().trim();

            if (field.prop('required')) {
                hasRequiredFields = true;
                if (value === '') {
                    allValid = false;
                }
            }
        });

        // Check image fields
        customizationContainer.find('.wpc-image-field').each(function() {
            const imageField = $(this);
            const fileInput = imageField.find('.wpc-file-input');
            const preview = imageField.find('.wpc-image-preview');

            if (fileInput.prop('required')) {
                hasRequiredFields = true;
                // Check if file is uploaded (either has files or preview is visible)
                if (!fileInput[0].files.length && !preview.is(':visible')) {
                    allValid = false;
                }
            }
        });

        // Update add to cart button state
        updateAddToCartButton(allValid, hasRequiredFields);

        // Reset recursion flag
        window.wpcValidating = false;

        return allValid;
    }

    // Update add to cart button state
    function updateAddToCartButton(isValid, hasRequiredFields) {
        const addToCartButton = $('.wpc-controlled-button');

        if (addToCartButton.length === 0) {
            return; // No button to control
        }

        const originalText = addToCartButton.data('original-text');

        // Only control button if there are required fields
        if (hasRequiredFields) {
            if (isValid) {
                // Enable button
                addToCartButton
                    .prop('disabled', false)
                    .removeClass('wpc-disabled')
                    .text(originalText);
            } else {
                // Disable button
                addToCartButton
                    .prop('disabled', true)
                    .addClass('wpc-disabled')
                    .text('Preencha os campos obrigatórios');
            }
        } else {
            // No required fields, ensure button is enabled
            addToCartButton
                .prop('disabled', false)
                .removeClass('wpc-disabled')
                .text(originalText);
        }
    }

    // Session Monitoring
    function initSessionMonitoring() {
        // Check if there are customization fields
        const customizationContainer = $('#wpc-customization-fields');
        if (customizationContainer.length === 0) {
            return;
        }

        // Check session every 2 minutes
        setInterval(checkSessionStatus, 2 * 60 * 1000);

        // Show warning at 12 minutes (3 minutes before expiry)
        setTimeout(showSessionWarning, 12 * 60 * 1000);
    }

    // Check session status
    function checkSessionStatus() {
        $.ajax({
            url: wpc_frontend.ajax_url,
            type: 'POST',
            data: {
                action: 'wpc_check_session',
                nonce: wpc_frontend.nonce
            },
            success: function(response) {
                if (!response.success && response.data && response.data.expired) {
                    showSessionExpiredAlert(response.data.message);
                }
            },
            error: function() {
                // Silent fail - don't interrupt user experience
            }
        });
    }

    // Show session warning (3 minutes before expiry)
    function showSessionWarning() {
        // Check if user still has uploaded files
        const hasUploadedFiles = $('.wpc-image-preview:visible').length > 0;

        if (hasUploadedFiles) {
            showWordPressNotice(
                'Atenção: Sua sessão expirará em 3 minutos. Adicione o produto ao carrinho para salvar suas personalizações.',
                'warning'
            );
        }
    }

    // Show session expired alert
    function showSessionExpiredAlert(message) {
        showWordPressNotice(message, 'error');

        // Clear all uploaded files from interface
        $('.wpc-image-preview').hide();
        $('.wpc-upload-area').show();
        $('.wpc-file-input').val('');
        $('.wpc-uploaded-file').val('');

        // Reset add to cart button
        validateAllFields();
    }

    // Show WordPress-style notice
    function showWordPressNotice(message, type) {
        // Remove existing notices
        $('.wpc-session-notice').remove();

        const noticeClass = type === 'error' ? 'notice-error' : 'notice-warning';
        const notice = $(`
            <div class="notice ${noticeClass} wpc-session-notice" style="margin: 15px 0; padding: 12px; border-left: 4px solid; position: relative;">
                <p style="margin: 0;">${message}</p>
                <button type="button" class="notice-dismiss" style="position: absolute; top: 0; right: 1px; border: none; margin: 0; padding: 9px; background: none; color: #72777c; cursor: pointer;">
                    <span class="screen-reader-text">Dispensar este aviso.</span>
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `);

        // Insert notice before customization fields
        $('#wpc-customization-fields').before(notice);

        // Handle dismiss button
        notice.find('.notice-dismiss').on('click', function() {
            notice.fadeOut(300, function() {
                $(this).remove();
            });
        });

        // Auto-dismiss warning notices after 10 seconds
        if (type === 'warning') {
            setTimeout(function() {
                notice.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 10000);
        }
    }

    // Product Preview Functions
    function generateProductPreview(imageUrl) {
        // Check if preview is enabled
        if (!wpc_frontend.preview || !wpc_frontend.preview.enabled) {
            return;
        }

        const modal = $('#wpc-preview-modal');
        const canvas = $('#wpc-preview-canvas')[0];
        const loading = $('.wpc-preview-loading');

        if (!canvas || !modal.length) {
            return;
        }

        // Show modal and loading
        modal.show();
        loading.show();

        const ctx = canvas.getContext('2d');
        const templateImg = new Image();
        const userImg = new Image();
        const maskImg = new Image();

        let imagesLoaded = 0;
        const totalImages = wpc_frontend.preview.mask ? 3 : 2;

        function checkAllImagesLoaded() {
            imagesLoaded++;
            if (imagesLoaded === totalImages) {
                drawPreview();
            }
        }

        function drawPreview() {
            const area = wpc_frontend.preview.area;

            // Set canvas size based on template
            canvas.width = templateImg.width;
            canvas.height = templateImg.height;

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw template (background)
            ctx.drawImage(templateImg, 0, 0);

            // Draw user image in specified area
            if (area) {
                // Calculate aspect ratio to fit image in area
                const aspectRatio = userImg.width / userImg.height;
                const areaAspectRatio = area.width / area.height;

                let drawWidth, drawHeight, drawX, drawY;

                if (aspectRatio > areaAspectRatio) {
                    // Image is wider than area
                    drawWidth = area.width;
                    drawHeight = area.width / aspectRatio;
                    drawX = area.x;
                    drawY = area.y + (area.height - drawHeight) / 2;
                } else {
                    // Image is taller than area
                    drawHeight = area.height;
                    drawWidth = area.height * aspectRatio;
                    drawX = area.x + (area.width - drawWidth) / 2;
                    drawY = area.y;
                }

                // Save context for clipping
                ctx.save();

                // Create clipping path for the area
                ctx.beginPath();
                ctx.rect(area.x, area.y, area.width, area.height);
                ctx.clip();

                // Draw user image
                ctx.drawImage(userImg, drawX, drawY, drawWidth, drawHeight);

                // Restore context
                ctx.restore();
            } else {
                // No area specified, draw image centered
                const scale = Math.min(canvas.width / userImg.width, canvas.height / userImg.height) * 0.8;
                const scaledWidth = userImg.width * scale;
                const scaledHeight = userImg.height * scale;
                const x = (canvas.width - scaledWidth) / 2;
                const y = (canvas.height - scaledHeight) / 2;

                ctx.drawImage(userImg, x, y, scaledWidth, scaledHeight);
            }

            // Draw mask on top if available
            if (wpc_frontend.preview.mask && maskImg.complete) {
                ctx.drawImage(maskImg, 0, 0);
            }

            // Hide loading
            loading.hide();
        }

        // Load template image
        templateImg.onload = checkAllImagesLoaded;
        templateImg.onerror = function() {
            console.error('Failed to load template image');
            loading.hide();
        };
        templateImg.src = wpc_frontend.preview.template;

        // Load user image
        userImg.onload = checkAllImagesLoaded;
        userImg.onerror = function() {
            console.error('Failed to load user image');
            loading.hide();
        };
        userImg.src = imageUrl;

        // Load mask image if available
        if (wpc_frontend.preview.mask) {
            maskImg.onload = checkAllImagesLoaded;
            maskImg.onerror = function() {
                console.error('Failed to load mask image');
                checkAllImagesLoaded(); // Continue without mask
            };
            maskImg.src = wpc_frontend.preview.mask;
        }
    }

    // Preview button click handler
    $(document).on('click', '.wpc-preview-btn', function() {
        const imageUrl = $(this).data('image-url');
        if (imageUrl) {
            generateProductPreview(imageUrl);
        }
    });

    // Modal close handlers
    $(document).on('click', '.wpc-modal-close, .wpc-modal-overlay', function() {
        $('#wpc-preview-modal').hide();
    });

    // Prevent modal content click from closing modal
    $(document).on('click', '.wpc-modal-content', function(e) {
        e.stopPropagation();
    });

    // Hide preview button when image is removed
    $(document).on('click', '.wpc-remove-image', function() {
        const imageField = $(this).closest('.wpc-image-field');
        const previewBtn = imageField.find('.wpc-preview-btn');
        previewBtn.hide().removeData('image-url');
    });

    // ESC key to close modal
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('#wpc-preview-modal').hide();
        }
    });

    // Cart Restoration Functions
    function initCartRestoration() {
        // Check if we have restoration data from URL parameters
        const urlParams = new URLSearchParams(window.location.search);

        // Look for WPC parameters
        const wpcParams = {};
        let hasWpcData = false;

        for (const [key, value] of urlParams.entries()) {
            if (key.startsWith('wpc_')) {
                wpcParams[key] = value;
                hasWpcData = true;
            }
        }

        if (hasWpcData) {
            // Convert URL parameters to restoration data format
            const data = parseWpcParameters(wpcParams);
            restoreCustomizationData(data);

            // Clean WPC parameters from URL
            const cleanParams = new URLSearchParams(window.location.search);
            for (const key of Object.keys(wpcParams)) {
                cleanParams.delete(key);
            }

            const cleanUrl = window.location.pathname + (cleanParams.toString() ? '?' + cleanParams.toString() : '');
            window.history.replaceState({}, document.title, cleanUrl);

            // Show restoration notification
            showRestorationNotification();
        }
    }

    function parseWpcParameters(wpcParams) {
        const data = { fields: {} };

        // Group parameters by field ID
        const fieldGroups = {};

        for (const [key, value] of Object.entries(wpcParams)) {
            const match = key.match(/^wpc_(image|url|size|text)_(\d+)$/);
            if (match) {
                const [, type, fieldId] = match;

                if (!fieldGroups[fieldId]) {
                    fieldGroups[fieldId] = {};
                }

                if (type === 'image') {
                    fieldGroups[fieldId].type = 'image';
                    fieldGroups[fieldId].filename = value;
                } else if (type === 'url') {
                    fieldGroups[fieldId].url = value;
                } else if (type === 'size') {
                    fieldGroups[fieldId].size = value;
                } else if (type === 'text') {
                    fieldGroups[fieldId].type = 'text';
                    fieldGroups[fieldId].value = value;
                }
            }
        }

        // Convert to final format
        for (const [fieldId, fieldData] of Object.entries(fieldGroups)) {
            data.fields[fieldId] = fieldData;
        }

        return data;
    }

    function restoreCustomizationData(data) {
        // Wait for page to be fully loaded and check if elements exist
        function attemptRestore() {
            const customizationContainer = $('#wpc-customization-fields');
            if (customizationContainer.length === 0) {
                setTimeout(attemptRestore, 500);
                return;
            }

            // Restore customization fields (variations are handled by WooCommerce natively)
            if (data.fields) {
                Object.keys(data.fields).forEach(function(fieldId) {
                    const fieldData = data.fields[fieldId];
                    const field = $('.wpc-image-field').filter(function() {
                        return $(this).find('input[type="file"]').attr('name') === 'wpc_field_' + fieldId;
                    });

                    if (field.length > 0) {
                        if (fieldData.type === 'image' && (fieldData.url || fieldData.filename)) {
                            // If we only have filename, reconstruct the URL
                            if (!fieldData.url && fieldData.filename) {
                                const today = new Date();
                                const sessionId = 'test_session_' + today.getFullYear() + '_' +
                                                String(today.getMonth() + 1).padStart(2, '0') + '_' +
                                                String(today.getDate()).padStart(2, '0');
                                fieldData.url = window.location.origin + '/wp-content/uploads/wpc-temp/' + sessionId + '/' + fieldData.filename;
                                fieldData.size = fieldData.size || '';
                            }
                            restoreImageField(field, fieldData);
                        } else if (fieldData.type === 'text' && fieldData.value) {
                            restoreTextField(fieldId, fieldData.value);
                        }
                    }
                });
            }
        }

        // Start the restoration attempt
        attemptRestore();
    }



    function restoreImageField(imageField, fieldData) {
        const preview = imageField.find('.wpc-image-preview');
        const uploadArea = imageField.find('.wpc-upload-area');
        const thumbnail = preview.find('.wpc-thumbnail');
        const filename = preview.find('.wpc-filename');
        const filesize = preview.find('.wpc-filesize');
        const hiddenInput = imageField.find('.wpc-uploaded-file');
        const previewBtn = imageField.find('.wpc-preview-btn');

        // Set image data
        thumbnail.attr('src', fieldData.url);
        filename.text(fieldData.filename || 'Imagem restaurada');
        filesize.text(fieldData.size || '');
        hiddenInput.val(fieldData.filename || '');

        // Show preview and hide upload area
        uploadArea.hide();
        preview.show();

        // Mark field as valid
        imageField.removeClass('wpc-invalid').addClass('wpc-valid');

        // Show preview button if available
        if (previewBtn.length > 0) {
            previewBtn.show();
            previewBtn.data('image-url', fieldData.url);
        }

        // Reactivate image field functionality
        reactivateImageField(imageField);

        // Validate all fields after restoration
        validateAllFields();
    }

    function restoreTextField(fieldId, value) {
        const textField = $('input[name="wpc_field_' + fieldId + '"], textarea[name="wpc_field_' + fieldId + '"]');
        if (textField.length > 0) {
            textField.val(value);
            textField.removeClass('wpc-invalid').addClass('wpc-valid');
        }
    }

    function reactivateImageField(imageField) {
        const uploadArea = imageField.find('.wpc-upload-area');
        const fileInput = imageField.find('.wpc-file-input');
        const maxSize = parseFloat(imageField.data('max-size'));

        // Remove existing event listeners to avoid duplicates
        uploadArea.off('click dragover dragleave drop');
        fileInput.off('change');
        imageField.off('click', '.wpc-remove-image');

        // Click to upload
        uploadArea.on('click', function() {
            fileInput.click();
        });

        // File input change
        fileInput.on('change', function() {
            const file = this.files[0];
            if (file) {
                handleFileUpload(file, imageField, maxSize);
            }
        });

        // Drag and drop functionality
        uploadArea.on('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('wpc-dragover');
        });

        uploadArea.on('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('wpc-dragover');
        });

        uploadArea.on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('wpc-dragover');

            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0], imageField, maxSize);
            }
        });

        // Remove image functionality
        imageField.on('click', '.wpc-remove-image', function(e) {
            e.preventDefault();
            removeImage(imageField);

            // Check all fields after removal
            validateAllFields();
        });
    }

    function showRestorationNotification() {
        // Create notification element
        const notification = $(`
            <div class="wpc-restoration-notice">
                <div class="wpc-notice-content">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"></path>
                        <circle cx="12" cy="12" r="10"></circle>
                    </svg>
                    <span>✨ Personalização restaurada do carrinho!</span>
                </div>
            </div>
        `);

        // Add to page
        $('body').append(notification);

        // Show with animation
        setTimeout(function() {
            notification.addClass('wpc-notice-show');
        }, 100);

        // Hide after 4 seconds
        setTimeout(function() {
            notification.removeClass('wpc-notice-show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 4000);
    }

    // Force cart link modification via JavaScript
    function forceCartLinkModification() {
        console.log('WPC: Force cart link modification started');

        // Wait for page to be fully loaded
        setTimeout(function() {
            // Look for cart items with our customization indicator
            $('.woocommerce-cart-form .cart_item').each(function(index) {
                const cartItem = $(this);
                const productLink = cartItem.find('a[href*="/produto"]').first();

                if (productLink.length > 0) {
                    console.log('WPC: Found product link:', productLink.attr('href'));

                    // Check if this item has our customization (look for the emoji)
                    const hasCustomization = cartItem.text().includes('🎨 Imagem personalizada');

                    if (hasCustomization) {
                        console.log('WPC: Item has customization, modifying link');

                        // Get current URL
                        let currentUrl = productLink.attr('href');

                        // Create a simple restoration parameter based on what we know
                        const restorationParam = 'wpc_restore_cart_item=' + index;

                        // Add parameter to URL
                        const separator = currentUrl.includes('?') ? '&' : '?';
                        const newUrl = currentUrl + separator + restorationParam;

                        // Update the link
                        productLink.attr('href', newUrl);

                        console.log('WPC: Modified link to:', newUrl);

                        // Add visual indicator
                        if (!productLink.find('.wpc-modified').length) {
                            productLink.append(' <span class="wpc-modified" style="color: #0073aa; font-size: 12px;">🔄</span>');
                        }
                    }
                }
            });
        }, 1000);

        // Also try after AJAX updates
        $(document).on('updated_cart_totals', function() {
            console.log('WPC: Cart updated, re-running link modification');
            setTimeout(forceCartLinkModification, 500);
        });
    }

    // Handle the restoration parameter on product pages
    function handleCartRestoration() {
        const urlParams = new URLSearchParams(window.location.search);
        const restoreParam = urlParams.get('wpc_restore_cart_item');

        if (restoreParam !== null) {
            console.log('WPC: Cart restoration parameter found:', restoreParam);

            // Show a message that we're trying to restore
            if ($('#wpc-customization-fields').length > 0) {
                $('#wpc-customization-fields').prepend(
                    '<div class="wpc-restore-notice" style="background: #e7f3ff; border: 1px solid #0073aa; padding: 10px; margin-bottom: 15px; border-radius: 4px;">' +
                    '🔄 <strong>Restaurando personalização do carrinho...</strong><br>' +
                    '<small>Se a imagem não aparecer automaticamente, faça upload novamente.</small>' +
                    '</div>'
                );

                // Remove the notice after 5 seconds
                setTimeout(function() {
                    $('.wpc-restore-notice').fadeOut();
                }, 5000);
            }

            // Clean URL
            const cleanUrl = window.location.pathname + window.location.search.replace(/[?&]wpc_restore_cart_item=[^&]*/, '').replace(/^&/, '?');
            window.history.replaceState({}, document.title, cleanUrl);
        }
    }

    // Image Persistence System
    function initImagePersistence() {
        console.log('WPC: Initializing image persistence system');

        // Save image data when uploaded
        $(document).on('wpc_image_uploaded', function(e, fieldId, imageData) {
            saveImageToSession(fieldId, imageData);
        });

        // Restore images on page load
        restoreImagesFromSession();

        // Clear session data when form is successfully submitted
        $(document).on('wpc_form_submitted', function() {
            clearImageSession();
        });
    }

    function saveImageToSession(fieldId, imageData) {
        try {
            const sessionKey = 'wpc_images_' + getProductId();
            let sessionData = JSON.parse(sessionStorage.getItem(sessionKey) || '{}');

            sessionData[fieldId] = {
                filename: imageData.filename,
                url: imageData.url,
                size: imageData.size,
                timestamp: Date.now()
            };

            sessionStorage.setItem(sessionKey, JSON.stringify(sessionData));
            console.log('WPC: Saved image to session:', fieldId, imageData);
        } catch (e) {
            console.error('WPC: Error saving image to session:', e);
        }
    }

    function restoreImagesFromSession() {
        try {
            const sessionKey = 'wpc_images_' + getProductId();
            const sessionData = JSON.parse(sessionStorage.getItem(sessionKey) || '{}');

            if (Object.keys(sessionData).length > 0) {
                console.log('WPC: Restoring images from session:', sessionData);

                Object.keys(sessionData).forEach(function(fieldId) {
                    const imageData = sessionData[fieldId];

                    // Check if image is not too old (15 minutes)
                    const maxAge = 15 * 60 * 1000; // 15 minutes
                    if (Date.now() - imageData.timestamp < maxAge) {
                        const field = $('.wpc-image-field').filter(function() {
                            return $(this).find('input[type="file"]').attr('name') === 'wpc_field_' + fieldId;
                        });

                        if (field.length > 0) {
                            restoreImageField(field, imageData);
                        }
                    } else {
                        console.log('WPC: Image too old, not restoring:', fieldId);
                        delete sessionData[fieldId];
                    }
                });

                // Update session data (remove old images)
                sessionStorage.setItem(sessionKey, JSON.stringify(sessionData));
            }
        } catch (e) {
            console.error('WPC: Error restoring images from session:', e);
        }
    }

    function clearImageSession() {
        try {
            const sessionKey = 'wpc_images_' + getProductId();
            sessionStorage.removeItem(sessionKey);
            console.log('WPC: Cleared image session');
        } catch (e) {
            console.error('WPC: Error clearing image session:', e);
        }
    }

    function getProductId() {
        // Try to get product ID from various sources
        const productIdInput = $('input[name="product_id"]');
        if (productIdInput.length > 0) {
            return productIdInput.val();
        }

        // Fallback: extract from URL
        const pathParts = window.location.pathname.split('/');
        return pathParts[pathParts.length - 2] || 'unknown';
    }

});

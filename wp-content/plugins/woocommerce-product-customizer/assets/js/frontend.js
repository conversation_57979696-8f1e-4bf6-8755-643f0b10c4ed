jQuery(document).ready(function($) {

    // Initialize customization fields
    initCustomizationFields();

    function initCustomizationFields() {
        // Initialize text fields
        initTextFields();

        // Initialize image upload fields
        initImageFields();

        // Validate form before add to cart
        validateFormOnSubmit();

        // Initialize add to cart button control
        initAddToCartControl();

        // Initial validation check
        validateAllFields();

        // Session monitoring temporarily disabled
        // initSessionMonitoring();
    }
    
    // Text Fields Functionality
    function initTextFields() {
        $('.wpc-text-field').each(function() {
            const field = $(this);
            const counter = field.siblings('.wpc-char-counter');
            const maxLength = parseInt(field.attr('maxlength'));
            
            // Update character counter
            field.on('input', function() {
                const currentLength = $(this).val().length;
                const current = counter.find('.wpc-current');
                
                current.text(currentLength);
                
                // Update counter styling
                counter.removeClass('wpc-warning wpc-error');
                if (currentLength > maxLength * 0.9) {
                    counter.addClass('wpc-warning');
                }
                if (currentLength >= maxLength) {
                    counter.addClass('wpc-error');
                }
                
                // Validate field
                validateTextField(field);

                // Check all fields after change
                validateAllFields();
            });

            // Initial validation
            validateTextField(field);
        });
    }
    
    // Image Fields Functionality
    function initImageFields() {
        $('.wpc-image-field').each(function() {
            const imageField = $(this);
            const uploadArea = imageField.find('.wpc-upload-area');
            const fileInput = imageField.find('.wpc-file-input');
            const preview = imageField.find('.wpc-image-preview');
            const maxSize = parseFloat(imageField.data('max-size'));
            
            // Click to upload
            uploadArea.on('click', function() {
                fileInput.click();
            });
            
            // File input change
            fileInput.on('change', function() {
                const file = this.files[0];
                if (file) {
                    handleFileUpload(file, imageField, maxSize);
                }
            });
            
            // Drag and drop functionality
            uploadArea.on('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('wpc-dragover');
            });
            
            uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('wpc-dragover');
            });
            
            uploadArea.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('wpc-dragover');
                
                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0], imageField, maxSize);
                }
            });
            
            // Remove image functionality
            imageField.on('click', '.wpc-remove-image', function(e) {
                e.preventDefault();
                removeImage(imageField);

                // Check all fields after removal
                validateAllFields();
            });
        });
    }
    
    // Handle file upload
    function handleFileUpload(file, imageField, maxSize) {
        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
        if (!allowedTypes.includes(file.type)) {
            showError(imageField, wpc_frontend.strings.invalid_format);
            return;
        }
        
        // Validate file size
        const maxSizeBytes = maxSize * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            showError(imageField, wpc_frontend.strings.file_too_large + ' ' + maxSize + ' MB');
            return;
        }
        
        // Show loading state
        imageField.addClass('wpc-loading');
        showUploadProgress(imageField, 0);
        
        // Create FormData
        const formData = new FormData();
        formData.append('file', file);
        formData.append('field_id', getFieldId(imageField));
        formData.append('max_size', maxSize);
        formData.append('action', 'wpc_upload_file');
        formData.append('nonce', wpc_frontend.nonce);


        console.log('- AJAX URL:', wpc_frontend.ajax_url);
        
        // Upload file via AJAX
        $.ajax({
            url: wpc_frontend.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,

            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        showUploadProgress(imageField, percentComplete);
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                imageField.removeClass('wpc-loading');
                hideUploadProgress(imageField);

                if (response.success) {
                    showImagePreview(imageField, response.data);
                    clearError(imageField);

                    // Check all fields after successful upload
                    validateAllFields();
                } else {
                    showError(imageField, response.data.message);

                    // Check all fields after failed upload
                    validateAllFields();
                }
            },
            error: function(xhr, status, error) {
                imageField.removeClass('wpc-loading');
                hideUploadProgress(imageField);

                let errorMessage = wpc_frontend.strings.upload_error;
                if (xhr.status === 400) {
                    errorMessage = 'Erro 400: Requisição inválida. Verifique o arquivo selecionado.';
                } else if (xhr.status === 413) {
                    errorMessage = 'Erro 413: Arquivo muito grande para o servidor.';
                } else if (xhr.status === 500) {
                    errorMessage = 'Erro 500: Erro interno do servidor.';
                }

                showError(imageField, errorMessage);

                // Check all fields after error
                validateAllFields();
            }
        });
    }
    
    // Show image preview
    function showImagePreview(imageField, fileData) {
        const preview = imageField.find('.wpc-image-preview');
        const uploadArea = imageField.find('.wpc-upload-area');
        const thumbnail = preview.find('.wpc-thumbnail');
        const filename = preview.find('.wpc-filename');
        const filesize = preview.find('.wpc-filesize');
        const hiddenInput = imageField.find('.wpc-uploaded-file');

        // Set image data
        thumbnail.attr('src', fileData.url);
        filename.text(fileData.filename);
        filesize.text(fileData.size);
        hiddenInput.val(fileData.filename);

        // Hide upload area and show preview
        uploadArea.hide();
        preview.show();

        // Mark field as valid
        imageField.removeClass('wpc-invalid').addClass('wpc-valid');
    }
    
    // Remove image
    function removeImage(imageField) {
        const filename = imageField.find('.wpc-uploaded-file').val();
        
        if (filename) {
            // Delete file from server
            $.ajax({
                url: wpc_frontend.ajax_url,
                type: 'POST',
                data: {
                    action: 'wpc_delete_file',
                    filename: filename,
                    nonce: wpc_frontend.nonce
                }
            });
        }
        
        // Reset field
        const uploadArea = imageField.find('.wpc-upload-area');
        const preview = imageField.find('.wpc-image-preview');
        const fileInput = imageField.find('.wpc-file-input');
        const hiddenInput = imageField.find('.wpc-uploaded-file');

        // Hide preview and show upload area
        preview.hide();
        uploadArea.show();
        fileInput.val('');
        hiddenInput.val('');

        // Mark field as invalid
        imageField.removeClass('wpc-valid').addClass('wpc-invalid');
        
        clearError(imageField);
    }
    
    // Show upload progress
    function showUploadProgress(imageField, percent) {
        let progressContainer = imageField.find('.wpc-upload-progress');
        
        if (progressContainer.length === 0) {
            progressContainer = $(`
                <div class="wpc-upload-progress">
                    <div class="wpc-upload-progress-bar"></div>
                </div>
                <div class="wpc-upload-status">${wpc_frontend.strings.uploading}</div>
            `);
            imageField.find('.wpc-upload-area').after(progressContainer);
        }
        
        const progressBar = progressContainer.find('.wpc-upload-progress-bar');
        progressBar.css('width', percent + '%');
    }
    
    // Hide upload progress
    function hideUploadProgress(imageField) {
        imageField.find('.wpc-upload-progress, .wpc-upload-status').remove();
    }
    
    // Validate text field
    function validateTextField(field) {
        const value = field.val().trim();
        const isRequired = field.prop('required');
        
        if (isRequired && value === '') {
            field.removeClass('wpc-valid').addClass('wpc-invalid');
            return false;
        } else {
            field.removeClass('wpc-invalid').addClass('wpc-valid');
            return true;
        }
    }
    
    // Show error message
    function showError(field, message) {
        clearError(field);
        
        const errorDiv = $('<div class="wpc-error-message">' + message + '</div>');
        field.after(errorDiv);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            errorDiv.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }
    
    // Clear error message
    function clearError(field) {
        field.siblings('.wpc-error-message').remove();
    }
    
    // Get field ID from image field
    function getFieldId(imageField) {
        const fileInput = imageField.find('.wpc-file-input');
        const fieldId = fileInput.attr('id').replace('wpc_field_', '');
        return fieldId;
    }
    
    // Validate form before add to cart
    function validateFormOnSubmit() {
        $('form.cart').on('submit', function(e) {
            const customizationContainer = $('#wpc-customization-fields');
            
            if (customizationContainer.length === 0) {
                return true; // No customization fields
            }
            
            let isValid = true;
            const errors = [];
            
            // Validate text fields
            customizationContainer.find('.wpc-text-field').each(function() {
                if (!validateTextField($(this))) {
                    isValid = false;
                    errors.push(wpc_frontend.strings.required_field);
                }
            });
            
            // Validate image fields
            customizationContainer.find('.wpc-image-field').each(function() {
                const hiddenInput = $(this).find('.wpc-uploaded-file');
                if (hiddenInput.val() === '') {
                    isValid = false;
                    $(this).removeClass('wpc-valid').addClass('wpc-invalid');
                    errors.push(wpc_frontend.strings.required_field);
                }
            });
            
            // Show validation messages
            const messagesContainer = customizationContainer.find('.wpc-validation-messages');
            messagesContainer.empty();
            
            if (!isValid) {
                e.preventDefault();
                
                const uniqueErrors = [...new Set(errors)];
                uniqueErrors.forEach(function(error) {
                    messagesContainer.append('<div class="wpc-error-message">' + error + '</div>');
                });
                
                // Scroll to first error
                $('html, body').animate({
                    scrollTop: customizationContainer.offset().top - 100
                }, 500);
                
                return false;
            }
            
            return true;
        });
    }

    // Add to Cart Button Control
    function initAddToCartControl() {
        // Check if customization is required
        const customizationContainer = $('#wpc-customization-fields');
        if (customizationContainer.length === 0) {
            return; // No customization fields, nothing to control
        }

        // Add CSS class to identify controlled button
        const addToCartButton = $('button[name="add-to-cart"], .single_add_to_cart_button');
        addToCartButton.addClass('wpc-controlled-button');

        // Store original button text
        if (!addToCartButton.data('original-text')) {
            addToCartButton.data('original-text', addToCartButton.text());
        }
    }

    // Validate all customization fields
    function validateAllFields() {
        // Prevent infinite recursion
        if (window.wpcValidating) {
            return true;
        }
        window.wpcValidating = true;

        const customizationContainer = $('#wpc-customization-fields');
        if (customizationContainer.length === 0) {
            window.wpcValidating = false;
            return true; // No customization fields
        }

        let allValid = true;
        let hasRequiredFields = false;

        // Check text fields
        customizationContainer.find('.wpc-text-field').each(function() {
            const field = $(this);
            const value = field.val().trim();

            if (field.prop('required')) {
                hasRequiredFields = true;
                if (value === '') {
                    allValid = false;
                }
            }
        });

        // Check image fields
        customizationContainer.find('.wpc-image-field').each(function() {
            const imageField = $(this);
            const fileInput = imageField.find('.wpc-file-input');
            const preview = imageField.find('.wpc-image-preview');

            if (fileInput.prop('required')) {
                hasRequiredFields = true;
                // Check if file is uploaded (either has files or preview is visible)
                if (!fileInput[0].files.length && !preview.is(':visible')) {
                    allValid = false;
                }
            }
        });

        // Update add to cart button state
        updateAddToCartButton(allValid, hasRequiredFields);

        // Reset recursion flag
        window.wpcValidating = false;

        return allValid;
    }

    // Update add to cart button state
    function updateAddToCartButton(isValid, hasRequiredFields) {
        const addToCartButton = $('.wpc-controlled-button');

        if (addToCartButton.length === 0 || !hasRequiredFields) {
            return; // No button to control or no required fields
        }

        const originalText = addToCartButton.data('original-text');

        if (isValid) {
            // Enable button
            addToCartButton
                .prop('disabled', false)
                .removeClass('wpc-disabled')
                .text(originalText);
        } else {
            // Disable button
            addToCartButton
                .prop('disabled', true)
                .addClass('wpc-disabled')
                .text('Preencha os campos obrigatórios');
        }
    }

    // Session Monitoring
    function initSessionMonitoring() {
        // Check if there are customization fields
        const customizationContainer = $('#wpc-customization-fields');
        if (customizationContainer.length === 0) {
            return;
        }

        // Check session every 2 minutes
        setInterval(checkSessionStatus, 2 * 60 * 1000);

        // Show warning at 12 minutes (3 minutes before expiry)
        setTimeout(showSessionWarning, 12 * 60 * 1000);
    }

    // Check session status
    function checkSessionStatus() {
        $.ajax({
            url: wpc_frontend.ajax_url,
            type: 'POST',
            data: {
                action: 'wpc_check_session',
                nonce: wpc_frontend.nonce
            },
            success: function(response) {
                if (!response.success && response.data && response.data.expired) {
                    showSessionExpiredAlert(response.data.message);
                }
            },
            error: function() {
                // Silent fail - don't interrupt user experience
            }
        });
    }

    // Show session warning (3 minutes before expiry)
    function showSessionWarning() {
        // Check if user still has uploaded files
        const hasUploadedFiles = $('.wpc-image-preview:visible').length > 0;

        if (hasUploadedFiles) {
            showWordPressNotice(
                'Atenção: Sua sessão expirará em 3 minutos. Adicione o produto ao carrinho para salvar suas personalizações.',
                'warning'
            );
        }
    }

    // Show session expired alert
    function showSessionExpiredAlert(message) {
        showWordPressNotice(message, 'error');

        // Clear all uploaded files from interface
        $('.wpc-image-preview').hide();
        $('.wpc-upload-area').show();
        $('.wpc-file-input').val('');
        $('.wpc-uploaded-file').val('');

        // Reset add to cart button
        validateAllFields();
    }

    // Show WordPress-style notice
    function showWordPressNotice(message, type) {
        // Remove existing notices
        $('.wpc-session-notice').remove();

        const noticeClass = type === 'error' ? 'notice-error' : 'notice-warning';
        const notice = $(`
            <div class="notice ${noticeClass} wpc-session-notice" style="margin: 15px 0; padding: 12px; border-left: 4px solid; position: relative;">
                <p style="margin: 0;">${message}</p>
                <button type="button" class="notice-dismiss" style="position: absolute; top: 0; right: 1px; border: none; margin: 0; padding: 9px; background: none; color: #72777c; cursor: pointer;">
                    <span class="screen-reader-text">Dispensar este aviso.</span>
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `);

        // Insert notice before customization fields
        $('#wpc-customization-fields').before(notice);

        // Handle dismiss button
        notice.find('.notice-dismiss').on('click', function() {
            notice.fadeOut(300, function() {
                $(this).remove();
            });
        });

        // Auto-dismiss warning notices after 10 seconds
        if (type === 'warning') {
            setTimeout(function() {
                notice.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 10000);
        }
    }



});

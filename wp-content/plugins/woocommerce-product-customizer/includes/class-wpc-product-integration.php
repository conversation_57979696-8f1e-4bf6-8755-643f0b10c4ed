<?php
/**
 * Product integration class for WooCommerce Product Customizer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WPC_Product_Integration {

    /**
     * Constructor
     */
    public function __construct() {
        // Add product data tab
        add_filter('woocommerce_product_data_tabs', array($this, 'add_product_data_tab'));

        // Add product data panel
        add_action('woocommerce_product_data_panels', array($this, 'add_product_data_panel'));

        // Save product data
        add_action('woocommerce_process_product_meta', array($this, 'save_product_data'));

        // Add admin scripts for product edit page
        add_action('admin_enqueue_scripts', array($this, 'enqueue_product_scripts'));
    }

    /**
     * Add custom tab to product data tabs
     */
    public function add_product_data_tab($tabs) {
        $tabs['wpc_customization'] = array(
            'label'    => __('Personalizado', 'woocommerce-product-customizer'),
            'target'   => 'wpc_customization_product_data',
            'class'    => array('show_if_simple', 'show_if_variable', 'show_if_external', 'show_if_grouped'),
            'priority' => 80,
        );

        return $tabs;
    }

    /**
     * Add custom panel to product data panels
     */
    public function add_product_data_panel() {
        global $post;
        
        // Get available customizations
        $customizations = WPC_Database::get_customizations('active');
        
        // Get current product customizations
        $product_customizations = $this->get_product_customizations($post->ID);
        
        ?>
        <div id="wpc_customization_product_data" class="panel woocommerce_options_panel">
            <div class="options_group">
                <h3><?php _e('Configurações de Personalização', WPC_TEXT_DOMAIN); ?></h3>
                
                <?php if (empty($customizations)): ?>
                    <div class="wpc-notice">
                        <p>
                            <?php _e('Nenhuma personalização ativa encontrada.', WPC_TEXT_DOMAIN); ?>
                            <a href="<?php echo admin_url('admin.php?page=wpc-customizations'); ?>" target="_blank">
                                <?php _e('Criar personalização', WPC_TEXT_DOMAIN); ?>
                            </a>
                        </p>
                    </div>
                <?php else: ?>
                    
                    <div class="wpc-checkbox-row">
                        <p class="form-field wpc-checkbox-field">
                            <label for="wpc_enable_customization">
                                <input type="checkbox"
                                       id="wpc_enable_customization"
                                       name="wpc_enable_customization"
                                       value="yes"
                                       <?php checked(get_post_meta($post->ID, '_wpc_enable_customization', true), 'yes'); ?>>
                                <?php _e('Habilitar personalização para este produto', 'woocommerce-product-customizer'); ?>
                            </label>
                        </p>

                        <p class="form-field wpc-checkbox-field">
                            <label for="wpc_customization_required">
                                <input type="checkbox"
                                       id="wpc_customization_required"
                                       name="wpc_customization_required"
                                       value="yes"
                                       <?php checked(get_post_meta($post->ID, '_wpc_customization_required', true), 'yes'); ?>>
                                <?php _e('Personalização obrigatória', 'woocommerce-product-customizer'); ?>
                            </label>
                        </p>
                    </div>

                    <div id="wpc_customization_options" style="<?php echo get_post_meta($post->ID, '_wpc_enable_customization', true) === 'yes' ? '' : 'display: none;'; ?>">
                        
                        <div class="wpc-customizations-selector">
                            <p class="form-field">
                                <label for="wpc_customization_select">
                                    <?php _e('Selecione as personalizações disponíveis:', 'woocommerce-product-customizer'); ?>
                                </label>
                                <select id="wpc_customization_select" class="wc-enhanced-select">
                                    <option value=""><?php _e('Escolha uma personalização...', 'woocommerce-product-customizer'); ?></option>
                                    <?php foreach ($customizations as $customization): ?>
                                        <option value="<?php echo $customization->id; ?>"
                                                data-name="<?php echo esc_attr($customization->name); ?>">
                                            <?php echo esc_html($customization->name); ?>
                                            <?php
                                            $fields = WPC_Database::get_customization_fields($customization->id);
                                            $field_count = count($fields);
                                            printf(' (%d %s)', $field_count, _n('campo', 'campos', $field_count, 'woocommerce-product-customizer'));
                                            ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </p>

                            <div id="wpc_selected_customizations" class="wpc-selected-list">
                                <h4><?php _e('Personalizações Selecionadas:', 'woocommerce-product-customizer'); ?></h4>
                                <div id="wpc_selected_items">
                                    <?php if (!empty($product_customizations)): ?>
                                        <?php foreach ($product_customizations as $customization_id): ?>
                                            <?php
                                            $customization = WPC_Database::get_customization($customization_id);
                                            if ($customization && $customization->status === 'active'):
                                                $fields = WPC_Database::get_customization_fields($customization_id);
                                            ?>
                                                <div class="wpc-selected-item" data-id="<?php echo $customization_id; ?>">
                                                    <input type="hidden" name="wpc_customizations[]" value="<?php echo $customization_id; ?>">
                                                    <div class="wpc-item-header">
                                                        <strong><?php echo esc_html($customization->name); ?></strong>
                                                        <button type="button" class="wpc-remove-item button-link-delete">
                                                            <?php _e('Remover', 'woocommerce-product-customizer'); ?>
                                                        </button>
                                                    </div>
                                                    <div class="wpc-item-fields">
                                                        <?php if (!empty($fields)): ?>
                                                            <ul>
                                                                <?php foreach ($fields as $field): ?>
                                                                    <li>
                                                                        <strong><?php echo esc_html($field->field_label); ?></strong>
                                                                        (<?php echo esc_html(ucfirst($field->field_type)); ?> -
                                                                        <?php _e('Máx:', 'woocommerce-product-customizer'); ?> <?php echo esc_html($field->max_size); ?>)
                                                                    </li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        <?php else: ?>
                                                            <p class="description"><?php _e('Nenhum campo configurado.', 'woocommerce-product-customizer'); ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <p class="description" id="wpc_no_customizations">
                                            <?php _e('Nenhuma personalização selecionada.', 'woocommerce-product-customizer'); ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Hidden data for JavaScript -->
                            <?php foreach ($customizations as $customization): ?>
                                <script type="application/json" id="wpc_customization_data_<?php echo $customization->id; ?>">
                                    {
                                        "id": <?php echo $customization->id; ?>,
                                        "name": "<?php echo esc_js($customization->name); ?>",
                                        "fields": [
                                            <?php
                                            $fields = WPC_Database::get_customization_fields($customization->id);
                                            $field_data = array();
                                            foreach ($fields as $field) {
                                                $field_data[] = sprintf(
                                                    '{"label": "%s", "type": "%s", "max_size": "%s"}',
                                                    esc_js($field->field_label),
                                                    esc_js($field->field_type),
                                                    esc_js($field->max_size)
                                                );
                                            }
                                            echo implode(',', $field_data);
                                            ?>
                                        ]
                                    }
                                </script>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="wpc-customization-settings">
                            <h4><?php _e('Configurações Adicionais', WPC_TEXT_DOMAIN); ?></h4>

                            <p class="form-field">
                                <label for="wpc_customization_price">
                                    <?php _e('Taxa adicional por personalização (R$)', WPC_TEXT_DOMAIN); ?>
                                </label>
                                <input type="number"
                                       id="wpc_customization_price"
                                       name="wpc_customization_price"
                                       value="<?php echo esc_attr(get_post_meta($post->ID, '_wpc_customization_price', true)); ?>"
                                       step="0.01"
                                       min="0"
                                       class="short">
                                <span class="description">
                                    <?php _e('Valor adicional cobrado por personalização (opcional)', WPC_TEXT_DOMAIN); ?>
                                </span>
                            </p>

                            <p class="form-field">
                                <label for="wpc_customization_instructions">
                                    <?php _e('Instruções para o cliente', WPC_TEXT_DOMAIN); ?>
                                </label>
                                <textarea id="wpc_customization_instructions"
                                          name="wpc_customization_instructions"
                                          rows="3"
                                          class="short"><?php echo esc_textarea(get_post_meta($post->ID, '_wpc_customization_instructions', true)); ?></textarea>
                                <span class="description">
                                    <?php _e('Instruções que serão exibidas para o cliente no frontend', WPC_TEXT_DOMAIN); ?>
                                </span>
                            </p>
                        </div>

                        <div class="wpc-preview-settings">
                            <h4><?php _e('🎨 Pré-visualização com Template', WPC_TEXT_DOMAIN); ?></h4>
                            <p class="description" style="margin-bottom: 15px;">
                                <?php _e('Configure um template do produto e uma máscara para mostrar uma pré-visualização em tempo real da arte aplicada ao produto.', WPC_TEXT_DOMAIN); ?>
                            </p>

                            <div class="wpc-preview-fields">
                                <div class="wpc-field-group">
                                    <p class="form-field">
                                        <label for="wpc_preview_template">
                                            <?php _e('🖼️ Template do Produto', WPC_TEXT_DOMAIN); ?>
                                        </label>
                                        <input type="url"
                                               id="wpc_preview_template"
                                               name="wpc_preview_template"
                                               value="<?php echo esc_attr(get_post_meta($post->ID, '_wpc_preview_template', true)); ?>"
                                               class="regular-text"
                                               placeholder="https://exemplo.com/template-produto.png">
                                        <button type="button" class="button wpc-upload-btn" data-target="wpc_preview_template">
                                            <?php _e('Selecionar Imagem', WPC_TEXT_DOMAIN); ?>
                                        </button>
                                        <span class="description">
                                            <?php _e('Imagem base do produto onde a arte será aplicada', WPC_TEXT_DOMAIN); ?>
                                        </span>
                                    </p>

                                    <div id="wpc_preview_template_preview" class="wpc-image-preview" style="<?php echo get_post_meta($post->ID, '_wpc_preview_template', true) ? '' : 'display: none;'; ?>">
                                        <img src="<?php echo esc_attr(get_post_meta($post->ID, '_wpc_preview_template', true)); ?>" style="max-width: 200px; height: auto; border: 1px solid #ddd; border-radius: 4px;">
                                        <button type="button" class="button-link wpc-remove-image" data-target="wpc_preview_template">
                                            <?php _e('Remover', WPC_TEXT_DOMAIN); ?>
                                        </button>
                                    </div>
                                </div>

                                <div class="wpc-field-group">
                                    <p class="form-field">
                                        <label for="wpc_preview_mask">
                                            <?php _e('🎭 Máscara de Área', WPC_TEXT_DOMAIN); ?>
                                        </label>
                                        <input type="url"
                                               id="wpc_preview_mask"
                                               name="wpc_preview_mask"
                                               value="<?php echo esc_attr(get_post_meta($post->ID, '_wpc_preview_mask', true)); ?>"
                                               class="regular-text"
                                               placeholder="https://exemplo.com/mascara-area.png">
                                        <button type="button" class="button wpc-upload-btn" data-target="wpc_preview_mask">
                                            <?php _e('Selecionar Imagem', WPC_TEXT_DOMAIN); ?>
                                        </button>
                                        <span class="description">
                                            <?php _e('Máscara PNG transparente que define a área onde a arte será aplicada', WPC_TEXT_DOMAIN); ?>
                                        </span>
                                    </p>

                                    <div id="wpc_preview_mask_preview" class="wpc-image-preview" style="<?php echo get_post_meta($post->ID, '_wpc_preview_mask', true) ? '' : 'display: none;'; ?>">
                                        <img src="<?php echo esc_attr(get_post_meta($post->ID, '_wpc_preview_mask', true)); ?>" style="max-width: 200px; height: auto; border: 1px solid #ddd; border-radius: 4px;">
                                        <button type="button" class="button-link wpc-remove-image" data-target="wpc_preview_mask">
                                            <?php _e('Remover', WPC_TEXT_DOMAIN); ?>
                                        </button>
                                    </div>
                                </div>

                                <div class="wpc-field-group">
                                    <p class="form-field">
                                        <label for="wpc_preview_area">
                                            <?php _e('📐 Área de Aplicação (x, y, largura, altura)', WPC_TEXT_DOMAIN); ?>
                                        </label>
                                        <input type="text"
                                               id="wpc_preview_area"
                                               name="wpc_preview_area"
                                               value="<?php echo esc_attr(get_post_meta($post->ID, '_wpc_preview_area', true)); ?>"
                                               class="regular-text"
                                               placeholder="100,50,300,200">
                                        <span class="description">
                                            <?php _e('Coordenadas em pixels: x,y,largura,altura (ex: 100,50,300,200)', WPC_TEXT_DOMAIN); ?>
                                        </span>
                                    </p>
                                </div>

                                <div class="wpc-field-group">
                                    <p class="form-field wpc-checkbox-field">
                                        <label for="wpc_enable_preview">
                                            <input type="checkbox"
                                                   id="wpc_enable_preview"
                                                   name="wpc_enable_preview"
                                                   value="yes"
                                                   <?php checked(get_post_meta($post->ID, '_wpc_enable_preview', true), 'yes'); ?>>
                                            <?php _e('✨ Habilitar pré-visualização em tempo real', WPC_TEXT_DOMAIN); ?>
                                        </label>
                                        <span class="description">
                                            <?php _e('Mostra uma pré-visualização da arte aplicada ao produto quando o cliente faz upload', WPC_TEXT_DOMAIN); ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    
                <?php endif; ?>
            </div>
        </div>
        
        <style>
        .wpc-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 12px;
            margin: 10px 0;
        }

        .wpc-notice p {
            margin: 0;
            color: #856404;
        }

        .wpc-checkbox-row {
            display: flex;
            gap: 20px;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .wpc-checkbox-field {
            flex: 1;
            margin: 0 !important;
        }

        .wpc-checkbox-field label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .wpc-customizations-selector {
            margin-top: 15px;
        }

        .wpc-selected-list {
            margin-top: 20px;
        }

        .wpc-selected-list h4 {
            margin: 15px 0 10px 0;
            color: #23282d;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
        }

        .wpc-selected-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
            border-left: 4px solid #0073aa;
        }

        .wpc-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .wpc-item-header strong {
            color: #0073aa;
            font-size: 14px;
        }

        .wpc-remove-item {
            color: #d63638 !important;
            text-decoration: none;
            font-size: 12px;
            padding: 2px 6px;
        }

        .wpc-remove-item:hover {
            color: #fff !important;
            background-color: #d63638;
            border-radius: 3px;
        }

        .wpc-item-fields {
            background: #fff;
            padding: 10px;
            border-radius: 3px;
            border: 1px solid #e0e0e0;
        }

        .wpc-item-fields ul {
            margin: 0;
            padding-left: 20px;
        }

        .wpc-item-fields li {
            margin: 4px 0;
            font-size: 12px;
            color: #666;
        }

        .wpc-customization-settings {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }

        .wpc-customization-settings h4 {
            margin: 15px 0 10px 0;
            color: #23282d;
        }

        .wpc-preview-settings {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #e1e5e9;
        }

        .wpc-preview-settings h4 {
            margin: 0 0 10px 0;
            color: #0073aa;
            font-size: 16px;
        }

        .wpc-preview-fields {
            display: grid;
            gap: 20px;
        }

        .wpc-field-group {
            background: #fff;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .wpc-field-group .form-field {
            margin-bottom: 10px;
        }

        .wpc-upload-btn {
            margin-left: 10px;
            vertical-align: top;
        }

        .wpc-image-preview {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }

        .wpc-image-preview img {
            display: block;
            margin: 0 auto 10px auto;
        }

        .wpc-remove-image {
            color: #d63638;
            text-decoration: none;
            font-size: 12px;
        }

        .wpc-remove-image:hover {
            color: #d63638;
            text-decoration: underline;
        }

        #wpc_customization_select {
            min-width: 300px;
        }

        @media screen and (max-width: 782px) {
            .wpc-checkbox-row {
                flex-direction: column;
                gap: 10px;
            }

            .wpc-item-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }
        </style>
        <?php
    }

    /**
     * Save product customization data
     */
    public function save_product_data($post_id) {
        // Save enable customization
        $enable_customization = isset($_POST['wpc_enable_customization']) ? 'yes' : 'no';
        update_post_meta($post_id, '_wpc_enable_customization', $enable_customization);

        // Save required customization
        $customization_required = isset($_POST['wpc_customization_required']) ? 'yes' : 'no';
        update_post_meta($post_id, '_wpc_customization_required', $customization_required);

        // Save selected customizations
        $customizations = isset($_POST['wpc_customizations']) ? array_map('intval', $_POST['wpc_customizations']) : array();
        update_post_meta($post_id, '_wpc_customizations', $customizations);

        // Save customization price
        $customization_price = isset($_POST['wpc_customization_price']) ? floatval($_POST['wpc_customization_price']) : 0;
        update_post_meta($post_id, '_wpc_customization_price', $customization_price);

        // Save customization instructions
        $customization_instructions = isset($_POST['wpc_customization_instructions']) ? sanitize_textarea_field($_POST['wpc_customization_instructions']) : '';
        update_post_meta($post_id, '_wpc_customization_instructions', $customization_instructions);

        // Save preview settings
        $preview_template = isset($_POST['wpc_preview_template']) ? esc_url_raw($_POST['wpc_preview_template']) : '';
        update_post_meta($post_id, '_wpc_preview_template', $preview_template);

        $preview_mask = isset($_POST['wpc_preview_mask']) ? esc_url_raw($_POST['wpc_preview_mask']) : '';
        update_post_meta($post_id, '_wpc_preview_mask', $preview_mask);

        $preview_area = isset($_POST['wpc_preview_area']) ? sanitize_text_field($_POST['wpc_preview_area']) : '';
        update_post_meta($post_id, '_wpc_preview_area', $preview_area);

        $enable_preview = isset($_POST['wpc_enable_preview']) ? 'yes' : 'no';
        update_post_meta($post_id, '_wpc_enable_preview', $enable_preview);
    }

    /**
     * Get product customizations
     */
    public function get_product_customizations($product_id) {
        $customizations = get_post_meta($product_id, '_wpc_customizations', true);
        return is_array($customizations) ? $customizations : array();
    }

    /**
     * Enqueue scripts for product edit page
     */
    public function enqueue_product_scripts($hook) {
        global $post;
        
        if ($hook !== 'post.php' && $hook !== 'post-new.php') {
            return;
        }
        
        if (!$post || $post->post_type !== 'product') {
            return;
        }
        
        wp_enqueue_script(
            'wpc-product-admin',
            WPC_PLUGIN_URL . 'assets/js/product-admin.js',
            array('jquery'),
            WPC_VERSION,
            true
        );
    }

    /**
     * Check if product has customizations enabled
     */
    public static function is_product_customizable($product_id) {
        return get_post_meta($product_id, '_wpc_enable_customization', true) === 'yes';
    }

    /**
     * Check if customization is required for product
     */
    public static function is_customization_required($product_id) {
        return get_post_meta($product_id, '_wpc_customization_required', true) === 'yes';
    }

    /**
     * Get product customization price
     */
    public static function get_customization_price($product_id) {
        return floatval(get_post_meta($product_id, '_wpc_customization_price', true));
    }

    /**
     * Get product customization instructions
     */
    public static function get_customization_instructions($product_id) {
        return get_post_meta($product_id, '_wpc_customization_instructions', true);
    }

    /**
     * Check if preview is enabled for product
     */
    public static function is_preview_enabled($product_id) {
        return get_post_meta($product_id, '_wpc_enable_preview', true) === 'yes';
    }

    /**
     * Get product preview template URL
     */
    public static function get_preview_template($product_id) {
        return get_post_meta($product_id, '_wpc_preview_template', true);
    }

    /**
     * Get product preview mask URL
     */
    public static function get_preview_mask($product_id) {
        return get_post_meta($product_id, '_wpc_preview_mask', true);
    }

    /**
     * Get product preview area coordinates
     */
    public static function get_preview_area($product_id) {
        $area = get_post_meta($product_id, '_wpc_preview_area', true);
        if (empty($area)) {
            return null;
        }

        $coords = explode(',', $area);
        if (count($coords) !== 4) {
            return null;
        }

        return array(
            'x' => intval(trim($coords[0])),
            'y' => intval(trim($coords[1])),
            'width' => intval(trim($coords[2])),
            'height' => intval(trim($coords[3]))
        );
    }

    /**
     * Get all preview settings for a product
     */
    public static function get_preview_settings($product_id) {
        return array(
            'enabled' => self::is_preview_enabled($product_id),
            'template' => self::get_preview_template($product_id),
            'mask' => self::get_preview_mask($product_id),
            'area' => self::get_preview_area($product_id)
        );
    }
}

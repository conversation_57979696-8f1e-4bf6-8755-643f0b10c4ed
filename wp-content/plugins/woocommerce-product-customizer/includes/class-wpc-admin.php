<?php
/**
 * Admin functionality class
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WPC_Admin {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_init', array($this, 'handle_admin_actions'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Personalizados', WPC_TEXT_DOMAIN),
            __('Personalizados', WPC_TEXT_DOMAIN),
            'manage_woocommerce',
            'wpc-customizations',
            array($this, 'customizations_page'),
            'dashicons-admin-customizer',
            56
        );

        add_submenu_page(
            'wpc-customizations',
            __('Adicionar Personalização', WPC_TEXT_DOMAIN),
            __('Adicionar Personalização', WPC_TEXT_DOMAIN),
            'manage_woocommerce',
            'wpc-add-customization',
            array($this, 'add_customization_page')
        );

        add_submenu_page(
            'wpc-customizations',
            __('Configurações', WPC_TEXT_DOMAIN),
            __('Configurações', WPC_TEXT_DOMAIN),
            'manage_woocommerce',
            'wpc-settings',
            array($this, 'settings_page')
        );
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'wpc-') === false) {
            return;
        }

        wp_enqueue_script(
            'wpc-admin-js',
            WPC_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            WPC_VERSION,
            true
        );

        wp_enqueue_style(
            'wpc-admin-css',
            WPC_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WPC_VERSION
        );

        wp_localize_script('wpc-admin-js', 'wpc_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wpc_admin_nonce'),
            'strings' => array(
                'confirm_delete' => __('Tem certeza que deseja excluir esta personalização?', WPC_TEXT_DOMAIN),
                'confirm_deactivate' => __('Tem certeza que deseja desativar esta personalização?', WPC_TEXT_DOMAIN),
                'confirm_activate' => __('Tem certeza que deseja ativar esta personalização?', WPC_TEXT_DOMAIN),
            )
        ));
    }

    /**
     * Handle admin actions
     */
    public function handle_admin_actions() {
        if (!isset($_GET['page']) || strpos($_GET['page'], 'wpc-') === false) {
            return;
        }

        // Handle form submissions
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handle_post_actions();
        }

        // Handle GET actions
        if (isset($_GET['action'])) {
            $this->handle_get_actions();
        }
    }

    /**
     * Handle POST actions
     */
    private function handle_post_actions() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'wpc_admin_action')) {
            wp_die(__('Ação não autorizada.', WPC_TEXT_DOMAIN));
        }

        if (isset($_POST['wpc_action'])) {
            switch ($_POST['wpc_action']) {
                case 'add_customization':
                    $this->handle_add_customization();
                    break;
                case 'edit_customization':
                    $this->handle_edit_customization();
                    break;
            }
        }
    }

    /**
     * Handle GET actions
     */
    private function handle_get_actions() {
        if (!wp_verify_nonce($_GET['_wpnonce'], 'wpc_admin_action')) {
            wp_die(__('Ação não autorizada.', WPC_TEXT_DOMAIN));
        }

        switch ($_GET['action']) {
            case 'edit':
                // Handled in the page display
                break;
            case 'toggle_status':
                $this->handle_toggle_status();
                break;
            case 'delete':
                $this->handle_delete_customization();
                break;
        }
    }

    /**
     * Handle add customization
     */
    private function handle_add_customization() {
        $customization_data = array(
            'name' => sanitize_text_field($_POST['customization_name'])
        );

        $fields_data = array();
        if (isset($_POST['fields']) && is_array($_POST['fields'])) {
            foreach ($_POST['fields'] as $field) {
                $fields_data[] = array(
                    'field_type' => sanitize_text_field($field['type']),
                    'field_label' => sanitize_text_field($field['label']),
                    'max_size' => floatval($field['max_size'])
                );
            }
        }

        $result = WPC_Customization::create_customization($customization_data, $fields_data);

        if ($result['success']) {
            wp_redirect(add_query_arg(array(
                'page' => 'wpc-customizations',
                'message' => 'created'
            ), admin_url('admin.php')));
            exit;
        } else {
            $this->admin_notices = $result['errors'];
        }
    }

    /**
     * Handle edit customization
     */
    private function handle_edit_customization() {
        $customization_id = intval($_POST['customization_id']);
        
        $customization_data = array(
            'name' => sanitize_text_field($_POST['customization_name'])
        );

        $fields_data = array();
        if (isset($_POST['fields']) && is_array($_POST['fields'])) {
            foreach ($_POST['fields'] as $field) {
                $fields_data[] = array(
                    'field_type' => sanitize_text_field($field['type']),
                    'field_label' => sanitize_text_field($field['label']),
                    'max_size' => floatval($field['max_size'])
                );
            }
        }

        $result = WPC_Customization::update_customization($customization_id, $customization_data, $fields_data);

        if ($result['success']) {
            wp_redirect(add_query_arg(array(
                'page' => 'wpc-customizations',
                'message' => 'updated'
            ), admin_url('admin.php')));
            exit;
        } else {
            $this->admin_notices = $result['errors'];
        }
    }

    /**
     * Handle toggle status
     */
    private function handle_toggle_status() {
        $customization_id = intval($_GET['id']);
        $result = WPC_Customization::toggle_customization_status($customization_id);

        $message = $result['success'] ? 'status_updated' : 'error';
        
        wp_redirect(add_query_arg(array(
            'page' => 'wpc-customizations',
            'message' => $message
        ), admin_url('admin.php')));
        exit;
    }

    /**
     * Handle delete customization
     */
    private function handle_delete_customization() {
        $customization_id = intval($_GET['id']);
        $result = WPC_Customization::delete_customization($customization_id);

        $message = $result['success'] ? 'deleted' : 'error';
        
        wp_redirect(add_query_arg(array(
            'page' => 'wpc-customizations',
            'message' => $message
        ), admin_url('admin.php')));
        exit;
    }

    /**
     * Display customizations page
     */
    public function customizations_page() {
        $customizations = WPC_Customization::get_customizations_with_field_count();
        
        include WPC_PLUGIN_PATH . 'templates/admin/customizations-list.php';
    }

    /**
     * Display add customization page
     */
    public function add_customization_page() {
        $editing = false;
        $customization = null;
        
        if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
            $editing = true;
            $customization = WPC_Customization::get_customization_with_fields(intval($_GET['id']));
            
            if (!$customization) {
                wp_die(__('Personalização não encontrada.', WPC_TEXT_DOMAIN));
            }
        }
        
        include WPC_PLUGIN_PATH . 'templates/admin/add-customization.php';
    }

    /**
     * Display admin notices
     */
    public function display_admin_notices() {
        if (isset($_GET['message'])) {
            $message = '';
            $type = 'success';
            
            switch ($_GET['message']) {
                case 'created':
                    $message = __('Personalização criada com sucesso.', WPC_TEXT_DOMAIN);
                    break;
                case 'updated':
                    $message = __('Personalização atualizada com sucesso.', WPC_TEXT_DOMAIN);
                    break;
                case 'status_updated':
                    $message = __('Status da personalização atualizado com sucesso.', WPC_TEXT_DOMAIN);
                    break;
                case 'deleted':
                    $message = __('Personalização excluída com sucesso.', WPC_TEXT_DOMAIN);
                    break;
                case 'error':
                    $message = __('Ocorreu um erro. Tente novamente.', WPC_TEXT_DOMAIN);
                    $type = 'error';
                    break;
            }
            
            if ($message) {
                echo '<div class="notice notice-' . $type . ' is-dismissible"><p>' . $message . '</p></div>';
            }
        }

        if (isset($this->admin_notices) && !empty($this->admin_notices)) {
            echo '<div class="notice notice-error is-dismissible">';
            foreach ($this->admin_notices as $notice) {
                echo '<p>' . esc_html($notice) . '</p>';
            }
            echo '</div>';
        }
    }

    /**
     * Settings page
     */
    public function settings_page() {
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['wpc_settings_nonce'])) {
            if (wp_verify_nonce($_POST['wpc_settings_nonce'], 'wpc_settings_action')) {
                $this->save_settings();
            }
        }

        $session_timeout = get_option('wpc_session_timeout', 15);
        $cleanup_frequency = get_option('wpc_cleanup_frequency', 'hourly');

        ?>
        <div class="wrap">
            <h1><?php _e('Configurações do Product Customizer', WPC_TEXT_DOMAIN); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field('wpc_settings_action', 'wpc_settings_nonce'); ?>

                <table class="form-table">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="wpc_session_timeout"><?php _e('Tempo de Sessão (minutos)', WPC_TEXT_DOMAIN); ?></label>
                            </th>
                            <td>
                                <input type="number"
                                       id="wpc_session_timeout"
                                       name="wpc_session_timeout"
                                       value="<?php echo esc_attr($session_timeout); ?>"
                                       min="5"
                                       max="120"
                                       class="regular-text" />
                                <p class="description">
                                    <?php _e('Tempo em minutos antes que arquivos temporários sejam removidos. Padrão: 15 minutos.', WPC_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="wpc_cleanup_frequency"><?php _e('Frequência de Limpeza', WPC_TEXT_DOMAIN); ?></label>
                            </th>
                            <td>
                                <select id="wpc_cleanup_frequency" name="wpc_cleanup_frequency">
                                    <option value="hourly" <?php selected($cleanup_frequency, 'hourly'); ?>><?php _e('A cada hora', WPC_TEXT_DOMAIN); ?></option>
                                    <option value="twicedaily" <?php selected($cleanup_frequency, 'twicedaily'); ?>><?php _e('Duas vezes por dia', WPC_TEXT_DOMAIN); ?></option>
                                    <option value="daily" <?php selected($cleanup_frequency, 'daily'); ?>><?php _e('Diariamente', WPC_TEXT_DOMAIN); ?></option>
                                </select>
                                <p class="description">
                                    <?php _e('Frequência com que o sistema verifica e remove arquivos temporários expirados.', WPC_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h2><?php _e('Estatísticas', WPC_TEXT_DOMAIN); ?></h2>

                <table class="form-table">
                    <tbody>
                        <tr>
                            <th scope="row"><?php _e('Arquivos Temporários', WPC_TEXT_DOMAIN); ?></th>
                            <td>
                                <?php
                                $temp_files = get_option('wpc_temp_files', array());
                                $total_sessions = count($temp_files);
                                $total_files = 0;
                                foreach ($temp_files as $session_data) {
                                    $total_files += count($session_data['files']);
                                }
                                ?>
                                <p><?php printf(__('%d sessões ativas com %d arquivos temporários', WPC_TEXT_DOMAIN), $total_sessions, $total_files); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Próxima Limpeza', WPC_TEXT_DOMAIN); ?></th>
                            <td>
                                <?php
                                $next_cleanup = wp_next_scheduled('wpc_cleanup_temp_files');
                                if ($next_cleanup) {
                                    echo '<p>' . sprintf(__('Agendada para: %s', WPC_TEXT_DOMAIN), date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $next_cleanup)) . '</p>';
                                } else {
                                    echo '<p>' . __('Não agendada', WPC_TEXT_DOMAIN) . '</p>';
                                }
                                ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button-primary" value="<?php _e('Salvar Configurações', WPC_TEXT_DOMAIN); ?>" />
                    <input type="submit" name="cleanup_now" id="cleanup_now" class="button-secondary" value="<?php _e('Executar Limpeza Agora', WPC_TEXT_DOMAIN); ?>" />
                </p>
            </form>
        </div>
        <?php
    }

    /**
     * Save settings
     */
    private function save_settings() {
        if (isset($_POST['cleanup_now'])) {
            // Execute cleanup immediately
            $frontend = new WPC_Frontend();
            $frontend->cleanup_all_expired_files();

            echo '<div class="notice notice-success is-dismissible"><p>' . __('Limpeza executada com sucesso!', WPC_TEXT_DOMAIN) . '</p></div>';
            return;
        }

        $session_timeout = intval($_POST['wpc_session_timeout']);
        $cleanup_frequency = sanitize_text_field($_POST['wpc_cleanup_frequency']);

        // Validate session timeout
        if ($session_timeout < 5 || $session_timeout > 120) {
            echo '<div class="notice notice-error is-dismissible"><p>' . __('Tempo de sessão deve estar entre 5 e 120 minutos.', WPC_TEXT_DOMAIN) . '</p></div>';
            return;
        }

        // Validate cleanup frequency
        $valid_frequencies = array('hourly', 'twicedaily', 'daily');
        if (!in_array($cleanup_frequency, $valid_frequencies)) {
            echo '<div class="notice notice-error is-dismissible"><p>' . __('Frequência de limpeza inválida.', WPC_TEXT_DOMAIN) . '</p></div>';
            return;
        }

        // Save settings
        update_option('wpc_session_timeout', $session_timeout);
        update_option('wpc_cleanup_frequency', $cleanup_frequency);

        // Reschedule cleanup if frequency changed
        $current_frequency = wp_get_schedule('wpc_cleanup_temp_files');
        if ($current_frequency !== $cleanup_frequency) {
            wp_clear_scheduled_hook('wpc_cleanup_temp_files');
            wp_schedule_event(time(), $cleanup_frequency, 'wpc_cleanup_temp_files');
        }

        echo '<div class="notice notice-success is-dismissible"><p>' . __('Configurações salvas com sucesso!', WPC_TEXT_DOMAIN) . '</p></div>';
    }
}

<?php
/**
 * Frontend functionality class
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WPC_Frontend {

    /**
     * Constructor
     */
    public function __construct() {
        // Always add AJAX endpoints (needed for both admin and frontend AJAX)
        $this->init_ajax_hooks();

        // Add frontend display hooks only when not in admin (unless it's AJAX)
        if (!is_admin() || (defined('DOING_AJAX') && DOING_AJAX)) {
            $this->init_frontend_hooks();
        }
    }

    /**
     * Initialize AJAX hooks (always loaded)
     */
    private function init_ajax_hooks() {
        add_action('wp_ajax_wpc_upload_file', array($this, 'handle_file_upload'));
        add_action('wp_ajax_nopriv_wpc_upload_file', array($this, 'handle_file_upload'));

        add_action('wp_ajax_wpc_delete_file', array($this, 'handle_file_deletion'));
        add_action('wp_ajax_nopriv_wpc_delete_file', array($this, 'handle_file_deletion'));
    }

    /**
     * Initialize frontend display hooks (only for frontend/AJAX)
     */
    private function init_frontend_hooks() {
        // Display customization fields on product page
        add_action('woocommerce_before_add_to_cart_button', array($this, 'display_customization_fields'), 10);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));

        // Cart and order functionality
        add_filter('woocommerce_add_cart_item_data', array($this, 'add_customization_to_cart'), 10, 3);
        add_filter('woocommerce_get_item_data', array($this, 'display_customization_in_cart'), 10, 2);
        add_filter('woocommerce_add_to_cart_validation', array($this, 'validate_customization'), 10, 3);
        add_action('woocommerce_before_calculate_totals', array($this, 'adjust_cart_item_price'));
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {

        // Add customization fields to product page (between variations and add to cart button)
        add_action('woocommerce_before_add_to_cart_button', array($this, 'display_customization_fields'), 10);
        
        // Enqueue frontend scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        
        // Handle AJAX file deletion
        add_action('wp_ajax_wpc_delete_file', array($this, 'handle_file_deletion'));
        add_action('wp_ajax_nopriv_wpc_delete_file', array($this, 'handle_file_deletion'));
        


        // Modify product link in cart to include restoration data
        add_filter('woocommerce_cart_item_permalink', array($this, 'modify_cart_item_permalink'), 10, 3);

        // Session management (temporarily disabled)
        // add_action('wp_ajax_wpc_check_session', array($this, 'check_session_expiry'));
        // add_action('wp_ajax_nopriv_wpc_check_session', array($this, 'check_session_expiry'));

        // Cleanup cron job
        add_action('wpc_cleanup_temp_files', array($this, 'cleanup_all_expired_files'));

        // Schedule cleanup based on admin settings
        $this->schedule_cleanup_task();
    }

    /**
     * Display customization fields on product page
     */
    public function display_customization_fields() {
        global $product, $post;

        // Try to get product ID from different sources
        $product_id = null;

        if ($product && is_object($product) && method_exists($product, 'get_id')) {
            $product_id = $product->get_id();
        } elseif ($post && is_object($post)) {
            $product_id = $post->ID;
        } else {
            // Try to get from query vars
            $queried_object = get_queried_object();
            if ($queried_object && isset($queried_object->ID)) {
                $product_id = $queried_object->ID;
            }
        }

        if (!$product_id) {
            return;
        }

        // Check customization directly
        $enable_customization = get_post_meta($product_id, '_wpc_enable_customization', true);

        if ($enable_customization !== 'yes') {
            return;
        }

        // Get product customizations
        $customization_ids = get_post_meta($product_id, '_wpc_customizations', true);

        if (empty($customization_ids) || !is_array($customization_ids)) {
            return;
        }
        
        // Get customization price
        $customization_price = floatval(get_post_meta($product_id, '_wpc_customization_price', true));

        // Get customization instructions
        $instructions = get_post_meta($product_id, '_wpc_customization_instructions', true);

        // Check if customization is required
        $is_required = get_post_meta($product_id, '_wpc_customization_required', true) === 'yes';
        
        ?>
        <div id="wpc-customization-fields" class="wpc-product-customization">
            <?php if ($instructions): ?>
                <div class="wpc-instructions">
                    <p><?php echo wp_kses_post($instructions); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="wpc-fields-container">
                <?php
                foreach ($customization_ids as $customization_id) {
                    $customization = WPC_Database::get_customization($customization_id);
                    if (!$customization || $customization->status !== 'active') {
                        continue;
                    }
                    
                    $fields = WPC_Database::get_customization_fields($customization_id);
                    if (empty($fields)) {
                        continue;
                    }
                    ?>
                    <div class="wpc-customization-group" data-customization-id="<?php echo $customization_id; ?>">
                        
                        <?php foreach ($fields as $field): ?>
                            <div class="wpc-field-wrapper" data-field-id="<?php echo $field->id; ?>">
                                <label class="wpc-field-label">
                                    <?php echo esc_html($field->field_label); ?>
                                    <span class="wpc-required">*</span>
                                    <br>
                                    <span class="wpc-field-info">
                                        <?php if ($field->field_type === 'image'): ?>
                                            (<?php printf(__('Tamanho Máx.: %s MB', 'woocommerce-product-customizer'), $field->max_size); ?>)
                                        <?php else: ?>
                                            (<?php printf(__('Tamanho Máx.: %s caracteres', 'woocommerce-product-customizer'), $field->max_size); ?>)
                                        <?php endif; ?>
                                    </span>
                                </label>
                                
                                <?php if ($field->field_type === 'text'): ?>
                                    <textarea
                                        name="wpc_field_<?php echo $field->id; ?>"
                                        id="wpc_field_<?php echo $field->id; ?>"
                                        class="wpc-text-field"
                                        maxlength="<?php echo intval($field->max_size); ?>"
                                        placeholder="<?php echo esc_attr($field->field_label); ?>"></textarea>
                                    <div class="wpc-char-counter">
                                        <span class="wpc-current">0</span>/<span class="wpc-max"><?php echo intval($field->max_size); ?></span>
                                    </div>
                                    
                                <?php elseif ($field->field_type === 'image'): ?>
                                    <div class="wpc-image-field" data-max-size="<?php echo floatval($field->max_size); ?>">
                                        <div class="wpc-upload-area">
                                            <div class="wpc-upload-content">
                                                <div class="wpc-upload-icon">
                                                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                                        <polyline points="7,10 12,15 17,10"></polyline>
                                                        <line x1="12" y1="15" x2="12" y2="3"></line>
                                                    </svg>
                                                </div>
                                            </div>
                                            <input
                                                type="file"
                                                name="wpc_field_<?php echo $field->id; ?>"
                                                id="wpc_field_<?php echo $field->id; ?>"
                                                class="wpc-file-input"
                                                accept=".png,.jpg,.jpeg">
                                        </div>
                                        <div class="wpc-upload-instructions">
                                            <p class="wpc-upload-text">
                                                <?php _e('Clique para selecionar ou arraste a imagem aqui', 'woocommerce-product-customizer'); ?>
                                            </p>
                                            <p class="wpc-upload-requirements">
                                                <?php _e('PNG ou JPG, mínimo 150 DPI', 'woocommerce-product-customizer'); ?>
                                            </p>
                                        </div>
                                        <div class="wpc-image-preview" style="display: none;">
                                            <div class="wpc-image-container">
                                                <img class="wpc-thumbnail" src="" alt="">
                                                <div class="wpc-image-actions">
                                                    <button type="button" class="wpc-remove-image" title="<?php _e('Remover imagem', 'woocommerce-product-customizer'); ?>">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                            <line x1="18" y1="6" x2="6" y2="18"></line>
                                                            <line x1="6" y1="6" x2="18" y2="18"></line>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="wpc-image-info">
                                                <span class="wpc-filename"></span>
                                                <span class="wpc-filesize"></span>
                                            </div>
                                            <?php
                                            // Add preview button if preview is enabled
                                            global $post;
                                            if ($post && WPC_Product_Integration::is_preview_enabled($post->ID)) {
                                                ?>
                                                <button type="button" class="wpc-preview-btn" style="display: none;">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                                        <circle cx="12" cy="12" r="3"></circle>
                                                    </svg>
                                                    <?php _e('🎨 Ver Pré-visualização', 'woocommerce-product-customizer'); ?>
                                                </button>
                                                <?php
                                            }
                                            ?>
                                        </div>
                                        <input type="hidden" class="wpc-uploaded-file" name="wpc_uploaded_<?php echo $field->id; ?>" value="">
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <?php
                }
                ?>
            </div>
            
            <div class="wpc-validation-messages"></div>

            <?php
            // Add preview modal if enabled
            global $post;
            if ($post && WPC_Product_Integration::is_preview_enabled($post->ID)) {
                $preview_settings = WPC_Product_Integration::get_preview_settings($post->ID);
                if ($preview_settings['template'] && $preview_settings['area']) {
                    ?>
                    <!-- Preview Modal -->
                    <div id="wpc-preview-modal" class="wpc-modal" style="display: none;">
                        <div class="wpc-modal-overlay"></div>
                        <div class="wpc-modal-content">
                            <div class="wpc-modal-header">
                                <h3><?php _e('🎨 Pré-visualização do Produto', 'woocommerce-product-customizer'); ?></h3>
                                <button type="button" class="wpc-modal-close">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="18" y1="6" x2="6" y2="18"></line>
                                        <line x1="6" y1="6" x2="18" y2="18"></line>
                                    </svg>
                                </button>
                            </div>
                            <div class="wpc-modal-body">
                                <div class="wpc-preview-canvas-wrapper">
                                    <canvas id="wpc-preview-canvas" width="400" height="400"></canvas>
                                    <div class="wpc-preview-loading" style="display: none;">
                                        <div class="wpc-spinner"></div>
                                        <span><?php _e('Gerando pré-visualização...', 'woocommerce-product-customizer'); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="wpc-modal-footer">
                                <p class="wpc-preview-note">
                                    <?php _e('Esta é uma pré-visualização aproximada. O resultado final pode variar.', 'woocommerce-product-customizer'); ?>
                                </p>
                                <button type="button" class="wpc-btn wpc-btn-primary wpc-modal-close">
                                    <?php _e('Fechar', 'woocommerce-product-customizer'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            }
            ?>
        </div>
        <?php
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts() {
        if (!is_product()) {
            return;
        }

        global $product, $post;

        // Try to get product ID from different sources
        $product_id = null;

        if ($product && is_object($product) && method_exists($product, 'get_id')) {
            $product_id = $product->get_id();
        } elseif ($post && is_object($post)) {
            $product_id = $post->ID;
        } else {
            // Try to get from query vars
            $queried_object = get_queried_object();
            if ($queried_object && isset($queried_object->ID)) {
                $product_id = $queried_object->ID;
            }
        }

        if (!$product_id) {
            return;
        }

        $enable_customization = get_post_meta($product_id, '_wpc_enable_customization', true);

        if ($enable_customization !== 'yes') {
            return;
        }
        
        wp_enqueue_script(
            'wpc-frontend',
            WPC_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            WPC_VERSION,
            true
        );
        
        wp_enqueue_style(
            'wpc-frontend',
            WPC_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            WPC_VERSION
        );
        
        $nonce = wp_create_nonce('wpc_frontend_nonce');

        // Get preview settings for current product
        global $post;
        $preview_settings = array();
        if ($post && $post->post_type === 'product') {
            $preview_settings = WPC_Product_Integration::get_preview_settings($post->ID);
        }

        wp_localize_script('wpc-frontend', 'wpc_frontend', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => $nonce,
            'strings' => array(
                'upload_error' => __('Erro no upload. Tente novamente.', 'woocommerce-product-customizer'),
                'file_too_large' => __('Arquivo muito grande. Máximo permitido:', 'woocommerce-product-customizer'),
                'invalid_format' => __('Formato inválido. Use apenas PNG ou JPG.', 'woocommerce-product-customizer'),
                'low_resolution' => __('Resolução muito baixa. Mínimo 150 DPI.', 'woocommerce-product-customizer'),
                'required_field' => __('Este campo é obrigatório.', 'woocommerce-product-customizer'),
                'uploading' => __('Enviando...', 'woocommerce-product-customizer'),
            ),
            'max_file_size' => wp_max_upload_size(),
            'preview' => $preview_settings,
        ));
    }

    /**
     * Handle file upload via AJAX
     */
    public function handle_file_upload() {
        // Verify nonce for security
        $nonce_check = wp_verify_nonce($_POST['nonce'], 'wpc_frontend_nonce');

        if (!$nonce_check) {
            wp_send_json_error(array('message' => __('Erro de segurança. Recarregue a página e tente novamente.', 'woocommerce-product-customizer')));
        }

        // Check if file was uploaded
        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(array('message' => __('Erro no upload do arquivo.', 'woocommerce-product-customizer')));
        }

        $file = $_FILES['file'];
        $field_id = intval($_POST['field_id']);
        $max_size = floatval($_POST['max_size']);

        // Validate file type
        $allowed_types = array('image/png', 'image/jpeg', 'image/jpg');
        if (!in_array($file['type'], $allowed_types)) {
            wp_send_json_error(array('message' => __('Formato de arquivo inválido. Use PNG ou JPG.', 'woocommerce-product-customizer')));
        }

        // Validate file size
        $max_size_bytes = $max_size * 1024 * 1024; // Convert MB to bytes
        if ($file['size'] > $max_size_bytes) {
            wp_send_json_error(array('message' => sprintf(__('Arquivo muito grande. Máximo: %s MB', 'woocommerce-product-customizer'), $max_size)));
        }

        // Validate image resolution
        $image_info = getimagesize($file['tmp_name']);
        if ($image_info === false) {
            wp_send_json_error(array('message' => __('Arquivo de imagem inválido.', 'woocommerce-product-customizer')));
        }

        // Check DPI (basic validation)
        $this->validate_image_dpi($file['tmp_name']);

        // Upload file
        $upload_result = $this->upload_customization_file($file, $field_id);

        if (is_wp_error($upload_result)) {
            wp_send_json_error(array('message' => $upload_result->get_error_message()));
        }

        wp_send_json_success($upload_result);
    }

    /**
     * Validate image DPI
     */
    private function validate_image_dpi($file_path) {
        // This is a basic implementation
        // For more accurate DPI validation, you might want to use a more sophisticated method
        $image_info = getimagesize($file_path);
        
        if (isset($image_info[0]) && isset($image_info[1])) {
            // Basic size check - images should be reasonably sized for 150 DPI
            $min_width = 300; // Minimum width for decent quality
            $min_height = 300; // Minimum height for decent quality
            
            if ($image_info[0] < $min_width || $image_info[1] < $min_height) {
                wp_send_json_error(array('message' => __('Resolução muito baixa. Use imagens com pelo menos 300x300 pixels.', 'woocommerce-product-customizer')));
            }
        }
    }

    /**
     * Upload customization file to temporary directory
     */
    private function upload_customization_file($file, $field_id) {
        $upload_dir = wp_upload_dir();

        // Create temporary directory structure
        $temp_dir = $upload_dir['basedir'] . '/wpc-temp';

        // Use simple directory structure for testing
        $session_id = 'test_session_' . date('Y_m_d');
        $session_dir = $temp_dir . '/' . $session_id;

        // Create directories if they don't exist
        if (!file_exists($temp_dir)) {
            if (!wp_mkdir_p($temp_dir)) {
                return new WP_Error('dir_creation_failed', __('Falha ao criar diretório de upload.', 'woocommerce-product-customizer'));
            }
        }

        if (!file_exists($session_dir)) {
            if (!wp_mkdir_p($session_dir)) {
                return new WP_Error('session_dir_failed', __('Falha ao criar diretório da sessão.', 'woocommerce-product-customizer'));
            }
        }

        // Generate unique filename
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'field_' . $field_id . '_' . time() . '_' . uniqid() . '.' . $file_extension;
        $file_path = $session_dir . '/' . $filename;

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $file_path)) {
            return new WP_Error('upload_failed', __('Falha ao salvar o arquivo.', 'woocommerce-product-customizer'));
        }

        // Store file info in session with timestamp (temporarily disabled for testing)
        // $this->store_temp_file_info($session_id, $filename, $field_id);

        // Build URL and ensure HTTPS if needed
        $file_url = $upload_dir['baseurl'] . '/wpc-temp/' . $session_id . '/' . $filename;
        $file_url = $this->ensure_https_url($file_url);

        // Return file info
        return array(
            'filename' => $filename,
            'url' => $file_url,
            'path' => $file_path,
            'size' => size_format($file['size']),
            'session_id' => $session_id,
        );
    }

    /**
     * Handle file deletion via AJAX
     */
    public function handle_file_deletion() {
        check_ajax_referer('wpc_frontend_nonce', 'nonce');
        
        $filename = sanitize_file_name($_POST['filename']);
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['basedir'] . '/wpc-customizations/' . $filename;
        
        if (file_exists($file_path)) {
            unlink($file_path);
        }
        
        wp_send_json_success();
    }

    /**
     * Add customization data to cart
     */
    public function add_customization_to_cart($cart_item_data, $product_id, $variation_id) {
        $enable_customization = get_post_meta($product_id, '_wpc_enable_customization', true);
        if ($enable_customization !== 'yes') {
            return $cart_item_data;
        }

        $customization_data = array();
        $customization_ids = get_post_meta($product_id, '_wpc_customizations', true);

        if (empty($customization_ids) || !is_array($customization_ids)) {
            return $cart_item_data;
        }

        foreach ($customization_ids as $customization_id) {
            $fields = WPC_Database::get_customization_fields($customization_id);

            foreach ($fields as $field) {
                $field_key = 'wpc_field_' . $field->id;
                $uploaded_key = 'wpc_uploaded_' . $field->id;

                if ($field->field_type === 'text' && isset($_POST[$field_key])) {
                    $value = sanitize_textarea_field($_POST[$field_key]);
                    if (!empty($value)) {
                        $customization_data[$field->id] = array(
                            'type' => 'text',
                            'label' => $field->field_label,
                            'value' => $value
                        );
                    }
                } elseif ($field->field_type === 'image' && isset($_POST[$uploaded_key])) {
                    $filename = sanitize_file_name($_POST[$uploaded_key]);
                    if (!empty($filename)) {
                        $upload_dir = wp_upload_dir();

                        // Build correct file path based on our temporary structure
                        $session_id = 'test_session_' . date('Y_m_d');
                        $file_url = $upload_dir['baseurl'] . '/wpc-temp/' . $session_id . '/' . $filename;
                        $file_url = $this->ensure_https_url($file_url);

                        $file_path = $upload_dir['basedir'] . '/wpc-temp/' . $session_id . '/' . $filename;

                        // Get file size if file exists
                        $file_size = '';
                        if (file_exists($file_path)) {
                            $size_bytes = filesize($file_path);
                            $file_size = size_format($size_bytes);
                        }

                        $customization_data[$field->id] = array(
                            'type' => 'image',
                            'label' => $field->field_label,
                            'filename' => $filename,
                            'url' => $file_url,
                            'size' => $file_size
                        );
                    }
                }
            }
        }

        if (!empty($customization_data)) {
            $cart_item_data['wpc_customization'] = $customization_data;

            // Add customization price
            $customization_price = floatval(get_post_meta($product_id, '_wpc_customization_price', true));
            if ($customization_price > 0) {
                $cart_item_data['wpc_customization_price'] = $customization_price;
            }
        }

        return $cart_item_data;
    }

    /**
     * Modify cart item permalink to include restoration data
     */
    public function modify_cart_item_permalink($permalink, $cart_item, $cart_item_key) {
        // Only modify if we have customization data
        if (!isset($cart_item['wpc_customization']) || empty($cart_item['wpc_customization'])) {
            return $permalink;
        }

        // Start with the base permalink (which already includes variation attributes)
        $url_parts = parse_url($permalink);
        $query_params = array();

        // Parse existing query parameters (variation attributes)
        if (isset($url_parts['query'])) {
            parse_str($url_parts['query'], $query_params);
        }

        // Add customization data as individual parameters
        foreach ($cart_item['wpc_customization'] as $field_id => $data) {
            if ($data['type'] === 'image' && !empty($data['filename'])) {
                $query_params['wpc_image_' . $field_id] = $data['filename'];
                $query_params['wpc_url_' . $field_id] = $data['url'];
                if (!empty($data['size'])) {
                    $query_params['wpc_size_' . $field_id] = $data['size'];
                }
            } elseif ($data['type'] === 'text' && !empty($data['value'])) {
                $query_params['wpc_text_' . $field_id] = $data['value'];
            }
        }

        // Rebuild URL
        $base_url = $url_parts['scheme'] . '://' . $url_parts['host'];
        if (isset($url_parts['port'])) {
            $base_url .= ':' . $url_parts['port'];
        }
        $base_url .= $url_parts['path'];

        if (!empty($query_params)) {
            $base_url .= '?' . http_build_query($query_params);
        }

        return $base_url;
    }

    /**
     * Display customization in cart
     */
    public function display_customization_in_cart($item_data, $cart_item) {
        if (!isset($cart_item['wpc_customization'])) {
            return $item_data;
        }

        foreach ($cart_item['wpc_customization'] as $field_id => $data) {
            if ($data['type'] === 'text') {
                $item_data[] = array(
                    'key'   => $data['label'],
                    'value' => $data['value'],
                );
            } elseif ($data['type'] === 'image') {
                $image_html = '<div style="display: flex; align-items: center; gap: 10px;">';
                $image_html .= '<img src="' . esc_url($data['url']) . '" style="max-width: 60px; height: auto; border-radius: 4px; border: 1px solid #ddd;" alt="' . esc_attr($data['label']) . '">';
                $image_html .= '<div style="font-size: 12px; color: #666;">';
                $image_html .= '<div>' . esc_html($data['filename']) . '</div>';
                if (!empty($data['size'])) {
                    $image_html .= '<div>' . esc_html($data['size']) . '</div>';
                }
                $image_html .= '</div>';
                $image_html .= '</div>';

                $item_data[] = array(
                    'key'   => $data['label'],
                    'value' => $image_html,
                );
            }
        }

        return $item_data;
    }

    /**
     * Validate customization before add to cart
     */
    public function validate_customization($passed, $product_id, $quantity) {
        $enable_customization = get_post_meta($product_id, '_wpc_enable_customization', true);
        if ($enable_customization !== 'yes') {
            return $passed;
        }

        $customization_ids = get_post_meta($product_id, '_wpc_customizations', true);
        if (empty($customization_ids) || !is_array($customization_ids)) {
            return $passed;
        }

        $is_required = get_post_meta($product_id, '_wpc_customization_required', true) === 'yes';
        $has_customization = false;
        $validation_errors = array();

        foreach ($customization_ids as $customization_id) {
            $fields = WPC_Database::get_customization_fields($customization_id);

            foreach ($fields as $field) {
                $field_key = 'wpc_field_' . $field->id;
                $uploaded_key = 'wpc_uploaded_' . $field->id;

                if ($field->field_type === 'text') {
                    if (isset($_POST[$field_key]) && !empty(trim($_POST[$field_key]))) {
                        $has_customization = true;
                    }
                    // Note: Text fields are not required by default unless specifically marked
                } elseif ($field->field_type === 'image') {
                    if (isset($_POST[$uploaded_key]) && !empty($_POST[$uploaded_key])) {
                        $has_customization = true;
                    }
                    // Note: Image fields are not required by default unless specifically marked
                }
            }
        }

        // Only validate if customization is required for this product
        if ($is_required && !$has_customization) {
            wc_add_notice(
                __('Este produto requer personalização. Por favor, preencha pelo menos um campo de personalização.', 'woocommerce-product-customizer'),
                'error'
            );
            $passed = false;
        }

        return $passed;
    }

    /**
     * Ensure URL uses HTTPS if site is using HTTPS
     */
    private function ensure_https_url($url) {
        if (is_ssl() && strpos($url, 'http://') === 0) {
            return str_replace('http://', 'https://', $url);
        }
        return $url;
    }

    /**
     * Adjust cart item price for customization
     */
    public function adjust_cart_item_price($cart) {
        if (is_admin() && !defined('DOING_AJAX')) {
            return;
        }

        foreach ($cart->get_cart() as $cart_item) {
            if (isset($cart_item['wpc_customization_price'])) {
                $product = $cart_item['data'];
                $original_price = $product->get_price();
                $customization_price = floatval($cart_item['wpc_customization_price']);
                $new_price = $original_price + $customization_price;

                $product->set_price($new_price);
            }
        }
    }

    /**
     * Get or create session ID for temporary files
     */
    private function get_or_create_session_id() {
        // Temporarily disabled session_start() to test if this causes the 400 error
        // if (!session_id()) {
        //     session_start();
        // }

        // Return a simple session ID for testing
        return 'test_session_' . date('Y_m_d');

        /*
        if (!isset($_SESSION['wpc_session_id'])) {
            $_SESSION['wpc_session_id'] = 'wpc_' . uniqid() . '_' . time();
            $_SESSION['wpc_session_start'] = time();
        }

        return $_SESSION['wpc_session_id'];
        */
    }

    /**
     * Store temporary file information
     */
    private function store_temp_file_info($session_id, $filename, $field_id) {
        $temp_files = get_option('wpc_temp_files', array());

        if (!isset($temp_files[$session_id])) {
            $temp_files[$session_id] = array(
                'created' => time(),
                'files' => array(),
            );
        }

        $temp_files[$session_id]['files'][$filename] = array(
            'field_id' => $field_id,
            'uploaded' => time(),
        );

        update_option('wpc_temp_files', $temp_files);
    }

    /**
     * Get session timeout in minutes (configurable)
     */
    private function get_session_timeout() {
        return get_option('wpc_session_timeout', 15); // Default 15 minutes
    }

    /**
     * Check if session has expired
     */
    public function check_session_expiry() {
        if (!session_id()) {
            session_start();
        }

        if (!isset($_SESSION['wpc_session_start'])) {
            return;
        }

        $timeout_minutes = $this->get_session_timeout();
        $timeout_seconds = $timeout_minutes * 60;
        $session_age = time() - $_SESSION['wpc_session_start'];

        if ($session_age > $timeout_seconds) {
            // Session expired
            $this->cleanup_expired_session($_SESSION['wpc_session_id']);

            // Clear session
            unset($_SESSION['wpc_session_id']);
            unset($_SESSION['wpc_session_start']);

            wp_send_json_error(array(
                'expired' => true,
                'message' => sprintf(
                    __('Sua sessão expirou após %d minutos. Por favor, faça upload das imagens novamente.', 'woocommerce-product-customizer'),
                    $timeout_minutes
                )
            ));
        }
    }

    /**
     * Cleanup expired session files
     */
    private function cleanup_expired_session($session_id) {
        $upload_dir = wp_upload_dir();
        $session_dir = $upload_dir['basedir'] . '/wpc-temp/' . $session_id;

        if (file_exists($session_dir)) {
            $this->delete_directory($session_dir);
        }

        // Remove from database
        $temp_files = get_option('wpc_temp_files', array());
        if (isset($temp_files[$session_id])) {
            unset($temp_files[$session_id]);
            update_option('wpc_temp_files', $temp_files);
        }
    }

    /**
     * Delete directory and all contents
     */
    private function delete_directory($dir) {
        if (!file_exists($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), array('.', '..'));
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->delete_directory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }

    /**
     * Schedule cleanup task based on admin settings
     */
    private function schedule_cleanup_task() {
        $cleanup_frequency = get_option('wpc_cleanup_frequency', 'hourly');

        // Clear existing scheduled task
        wp_clear_scheduled_hook('wpc_cleanup_temp_files');

        // Schedule new task with current frequency setting
        if (!wp_next_scheduled('wpc_cleanup_temp_files')) {
            wp_schedule_event(time(), $cleanup_frequency, 'wpc_cleanup_temp_files');
        }
    }

    /**
     * Cleanup all expired temporary files (cron job)
     */
    public function cleanup_all_expired_files() {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/wpc-temp';

        // Check if temp directory exists
        if (!file_exists($temp_dir)) {
            return;
        }

        $timeout_seconds = $this->get_session_timeout() * 60;
        $current_time = time();
        $cleaned_count = 0;

        // Scan temp directory for session folders
        $session_dirs = glob($temp_dir . '/*', GLOB_ONLYDIR);

        foreach ($session_dirs as $session_dir) {
            $session_id = basename($session_dir);

            // Check if session directory has expired files
            $files = glob($session_dir . '/*');
            $should_cleanup = false;

            foreach ($files as $file) {
                $file_age = $current_time - filemtime($file);
                if ($file_age > $timeout_seconds) {
                    $should_cleanup = true;
                    break;
                }
            }

            if ($should_cleanup) {
                $this->cleanup_expired_session($session_id);
                $cleaned_count++;
            }
        }

        // Also cleanup database records
        $temp_files = get_option('wpc_temp_files', array());
        $updated = false;

        foreach ($temp_files as $session_id => $session_data) {
            $session_age = $current_time - $session_data['created'];

            if ($session_age > $timeout_seconds) {
                unset($temp_files[$session_id]);
                $updated = true;
            }
        }

        if ($updated) {
            update_option('wpc_temp_files', $temp_files);
        }

        // Log cleanup activity (only in debug mode)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("WPC Cleanup: Removed {$cleaned_count} expired session directories");
        }
    }

    /**
     * Get cleanup status for admin display
     */
    public function get_cleanup_status() {
        $next_cleanup = wp_next_scheduled('wpc_cleanup_temp_files');
        $cleanup_frequency = get_option('wpc_cleanup_frequency', 'hourly');

        return array(
            'next_cleanup' => $next_cleanup,
            'frequency' => $cleanup_frequency,
            'is_scheduled' => $next_cleanup !== false,
            'temp_files_count' => $this->count_temp_files()
        );
    }

    /**
     * Count temporary files for status display
     */
    private function count_temp_files() {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/wpc-temp';

        if (!file_exists($temp_dir)) {
            return 0;
        }

        $count = 0;
        $session_dirs = glob($temp_dir . '/*', GLOB_ONLYDIR);

        foreach ($session_dirs as $session_dir) {
            $files = glob($session_dir . '/*');
            $count += count($files);
        }

        return $count;
    }


}

<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'MelhorEnvio\\Controllers\\AgenciesController' => $baseDir . '/Controllers/AgenciesController.php',
    'MelhorEnvio\\Controllers\\CartController' => $baseDir . '/Controllers/CartController.php',
    'MelhorEnvio\\Controllers\\ConfigurationController' => $baseDir . '/Controllers/ConfigurationController.php',
    'MelhorEnvio\\Controllers\\LocationsController' => $baseDir . '/Controllers/LocationsController.php',
    'MelhorEnvio\\Controllers\\NoticeFormController' => $baseDir . '/Controllers/NoticeFormController.php',
    'MelhorEnvio\\Controllers\\OrdersController' => $baseDir . '/Controllers/OrdersController.php',
    'MelhorEnvio\\Controllers\\PackageController' => $baseDir . '/Controllers/PackageController.php',
    'MelhorEnvio\\Controllers\\PathController' => $baseDir . '/Controllers/PathController.php',
    'MelhorEnvio\\Controllers\\PayloadsController' => $baseDir . '/Controllers/PayloadsController.php',
    'MelhorEnvio\\Controllers\\ProductsController' => $baseDir . '/Controllers/ProductsController.php',
    'MelhorEnvio\\Controllers\\QuotationController' => $baseDir . '/Controllers/QuotationController.php',
    'MelhorEnvio\\Controllers\\SessionsController' => $baseDir . '/Controllers/SessionsController.php',
    'MelhorEnvio\\Controllers\\ShowCalculatorProductPage' => $baseDir . '/Controllers/ShowCalculatorProductPage.php',
    'MelhorEnvio\\Controllers\\StatusController' => $baseDir . '/Controllers/StatusController.php',
    'MelhorEnvio\\Controllers\\TokenController' => $baseDir . '/Controllers/TokenController.php',
    'MelhorEnvio\\Controllers\\UsersController' => $baseDir . '/Controllers/UsersController.php',
    'MelhorEnvio\\Factory\\ProductServiceFactory' => $baseDir . '/Factory/ProductServiceFactory.php',
    'MelhorEnvio\\Helpers\\CpfHelper' => $baseDir . '/Helpers/CpfHelper.php',
    'MelhorEnvio\\Helpers\\DimensionsHelper' => $baseDir . '/Helpers/DimensionsHelper.php',
    'MelhorEnvio\\Helpers\\EscapeAllowedTags' => $baseDir . '/Helpers/EscapeAllowedTags.php',
    'MelhorEnvio\\Helpers\\ExtractNumberHelper' => $baseDir . '/Helpers/ExtractNumberHelper.php',
    'MelhorEnvio\\Helpers\\FormaterHelper' => $baseDir . '/Helpers/FormaterHelper.php',
    'MelhorEnvio\\Helpers\\MoneyHelper' => $baseDir . '/Helpers/MoneyHelper.php',
    'MelhorEnvio\\Helpers\\OptionsHelper' => $baseDir . '/Helpers/OptionsHelper.php',
    'MelhorEnvio\\Helpers\\PostalCodeHelper' => $baseDir . '/Helpers/PostalCodeHelper.php',
    'MelhorEnvio\\Helpers\\ProductVirtualHelper' => $baseDir . '/Helpers/ProductVirtualHelper.php',
    'MelhorEnvio\\Helpers\\SanitizeHelper' => $baseDir . '/Helpers/SanitizeHelper.php',
    'MelhorEnvio\\Helpers\\SessionHelper' => $baseDir . '/Helpers/SessionHelper.php',
    'MelhorEnvio\\Helpers\\TimeHelper' => $baseDir . '/Helpers/TimeHelper.php',
    'MelhorEnvio\\Helpers\\TranslateStatusHelper' => $baseDir . '/Helpers/TranslateStatusHelper.php',
    'MelhorEnvio\\Helpers\\WpNonceValidatorHelper' => $baseDir . '/Helpers/WpNonceValidatorHelper.php',
    'MelhorEnvio\\Models\\Address' => $baseDir . '/Models/Address.php',
    'MelhorEnvio\\Models\\Agency' => $baseDir . '/Models/Agency.php',
    'MelhorEnvio\\Models\\AgencyAzul' => $baseDir . '/Models/AgencyAzul.php',
    'MelhorEnvio\\Models\\AgencyLatam' => $baseDir . '/Models/AgencyLatam.php',
    'MelhorEnvio\\Models\\CalculatorShow' => $baseDir . '/Models/CalculatorShow.php',
    'MelhorEnvio\\Models\\Cart' => $baseDir . '/Models/Cart.php',
    'MelhorEnvio\\Models\\Log' => $baseDir . '/Models/Log.php',
    'MelhorEnvio\\Models\\Method' => $baseDir . '/Models/Method.php',
    'MelhorEnvio\\Models\\Option' => $baseDir . '/Models/Option.php',
    'MelhorEnvio\\Models\\Order' => $baseDir . '/Models/Order.php',
    'MelhorEnvio\\Models\\Payload' => $baseDir . '/Models/Payload.php',
    'MelhorEnvio\\Models\\Product' => $baseDir . '/Models/Product.php',
    'MelhorEnvio\\Models\\ResponseStatus' => $baseDir . '/Models/ResponseStatus.php',
    'MelhorEnvio\\Models\\Seller' => $baseDir . '/Models/Seller.php',
    'MelhorEnvio\\Models\\Session' => $baseDir . '/Models/Session.php',
    'MelhorEnvio\\Models\\ShippingCompany' => $baseDir . '/Models/ShippingCompany.php',
    'MelhorEnvio\\Models\\ShippingService' => $baseDir . '/Models/ShippingService.php',
    'MelhorEnvio\\Models\\Store' => $baseDir . '/Models/Store.php',
    'MelhorEnvio\\Models\\Token' => $baseDir . '/Models/Token.php',
    'MelhorEnvio\\Models\\UseInsurance' => $baseDir . '/Models/UseInsurance.php',
    'MelhorEnvio\\Models\\User' => $baseDir . '/Models/User.php',
    'MelhorEnvio\\Models\\Version' => $baseDir . '/Models/Version.php',
    'MelhorEnvio\\Services\\AgenciesSelectedService' => $baseDir . '/Services/AgenciesSelectedService.php',
    'MelhorEnvio\\Services\\AgenciesService' => $baseDir . '/Services/AgenciesService.php',
    'MelhorEnvio\\Services\\BalanceService' => $baseDir . '/Services/BalanceService.php',
    'MelhorEnvio\\Services\\BuyerService' => $baseDir . '/Services/BuyerService.php',
    'MelhorEnvio\\Services\\CalculateShippingMethodService' => $baseDir . '/Services/CalculateShippingMethodService.php',
    'MelhorEnvio\\Services\\CartService' => $baseDir . '/Services/CartService.php',
    'MelhorEnvio\\Services\\CartWooCommerceService' => $baseDir . '/Services/CartWooCommerceService.php',
    'MelhorEnvio\\Services\\CheckHealthService' => $baseDir . '/Services/CheckHealthService.php',
    'MelhorEnvio\\Services\\ClearDataStored' => $baseDir . '/Services/ClearDataStored.php',
    'MelhorEnvio\\Services\\ConfigurationsService' => $baseDir . '/Services/ConfigurationsService.php',
    'MelhorEnvio\\Services\\InvoiceService' => $baseDir . '/Services/InvoiceService.php',
    'MelhorEnvio\\Services\\ListOrderService' => $baseDir . '/Services/ListOrderService.php',
    'MelhorEnvio\\Services\\ListPluginsIncompatiblesService' => $baseDir . '/Services/ListPluginsIncompatiblesService.php',
    'MelhorEnvio\\Services\\LocationService' => $baseDir . '/Services/LocationService.php',
    'MelhorEnvio\\Services\\ManageRequestService' => $baseDir . '/Services/ManageRequestService.php',
    'MelhorEnvio\\Services\\NoticeFormService' => $baseDir . '/Services/NoticeFormService.php',
    'MelhorEnvio\\Services\\OptionsMethodShippingService' => $baseDir . '/Services/OptionsMethodShippingService.php',
    'MelhorEnvio\\Services\\OrderInvoicesService' => $baseDir . '/Services/OrderInvoicesService.php',
    'MelhorEnvio\\Services\\OrderQuotationService' => $baseDir . '/Services/OrderQuotationService.php',
    'MelhorEnvio\\Services\\OrderService' => $baseDir . '/Services/OrderService.php',
    'MelhorEnvio\\Services\\OrdersProductsService' => $baseDir . '/Services/OrdersProductsService.php',
    'MelhorEnvio\\Services\\PackageService' => $baseDir . '/Services/PackageService.php',
    'MelhorEnvio\\Services\\PayloadService' => $baseDir . '/Services/PayloadService.php',
    'MelhorEnvio\\Services\\ProcessAdditionalTaxService' => $baseDir . '/Services/ProcessAdditionalTaxService.php',
    'MelhorEnvio\\Services\\Products\\BundleService' => $baseDir . '/Services/Products/BundleService.php',
    'MelhorEnvio\\Services\\Products\\CompositeService' => $baseDir . '/Services/Products/CompositeService.php',
    'MelhorEnvio\\Services\\Products\\InvalidProduct' => $baseDir . '/Services/Products/InvalidProduct.php',
    'MelhorEnvio\\Services\\Products\\ProductsService' => $baseDir . '/Services/Products/ProductsService.php',
    'MelhorEnvio\\Services\\QuotationProductPageService' => $baseDir . '/Services/QuotationProductPageService.php',
    'MelhorEnvio\\Services\\QuotationService' => $baseDir . '/Services/QuotationService.php',
    'MelhorEnvio\\Services\\RequestService' => $baseDir . '/Services/RequestService.php',
    'MelhorEnvio\\Services\\RolesService' => $baseDir . '/Services/RolesService.php',
    'MelhorEnvio\\Services\\RouterService' => $baseDir . '/Services/RouterService.php',
    'MelhorEnvio\\Services\\SellerService' => $baseDir . '/Services/SellerService.php',
    'MelhorEnvio\\Services\\SessionNoticeService' => $baseDir . '/Services/SessionNoticeService.php',
    'MelhorEnvio\\Services\\ShippingClassService' => $baseDir . '/Services/ShippingClassService.php',
    'MelhorEnvio\\Services\\ShippingMelhorEnvioService' => $baseDir . '/Services/ShippingMelhorEnvioService.php',
    'MelhorEnvio\\Services\\ShortCodeService' => $baseDir . '/Services/ShortCodeService.php',
    'MelhorEnvio\\Services\\StoreService' => $baseDir . '/Services/StoreService.php',
    'MelhorEnvio\\Services\\TestService' => $baseDir . '/Services/TestService.php',
    'MelhorEnvio\\Services\\TokenService' => $baseDir . '/Services/TokenService.php',
    'MelhorEnvio\\Services\\TrackingService' => $baseDir . '/Services/TrackingService.php',
    'MelhorEnvio\\Services\\UserWooCommerceDataService' => $baseDir . '/Services/UserWooCommerceDataService.php',
    'MelhorEnvio\\Services\\WooCommerceService' => $baseDir . '/Services/WooCommerceService.php',
);

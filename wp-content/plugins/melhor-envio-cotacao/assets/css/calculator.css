    /* Style inputs, select elements and textareas */

    .containerCalculator .calculatorRow {
        width: 100%!important;
    }

    #calcular-frete-loader {
        display: none;
        justify-content: center;
        align-items: center;
    }

    .tableResult {
        display: none;
    }

    .containerCalculator input[type=text],
    .containerCalculator select,
    .containerCalculator textarea {
        width: 100%;
        padding: 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-sizing: border-box;
        resize: vertical;
    }

    /* Style the label to display next to the inputs */
    .containerCalculator label {
        padding: 12px 12px 12px 0;
        display: inline-block;
    }

    /* Style the submit button */
    .containerCalculator input[type=submit] {
        background-color: #333333;
        color: white;
        padding: 12px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        float: right;
    }

    /* Style the containerCalculator */
    .containerCalculator {
        border-radius: 5px;
        background-color: #f2f2f2;
        padding: 20px;
        margin-top: 10px;
        margin-bottom: 20px;
        width: 100%;
    }

    /* Floating column for labels: 25% width */
    .containerCalculator .col-25 {
        float: left;
        width: 25%;
        margin-top: 6px;
    }

    /* Floating column for inputs: 75% width */
    .containerCalculator .col-75 {
        float: left;
        width: 100%;
        margin-top: 6px;
    }

    /* Clear floats after the columns */
    .containerCalculator .row:after {
        content: "";
        display: table;
        clear: both;
    }

    /* Responsive layout - when the screen is less than 600px wide, make the two columns stack on top of each other instead of next to each other */
    @media screen and (max-width: 600px) {

        .containerCalculator .col-25,
        .containerCalculator .col-75,
        input[type=submit],
        .containerCalculator input[type=text] {
            width: 100%;
            margin-top: 10;
        }
    }
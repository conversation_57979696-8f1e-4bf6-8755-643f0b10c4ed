me-easing( name )
    if name == "in-quad"
        return cubic-bezier(0.55,0.085,0.68,0.53)
    if name == "in-cubic"
        return cubic-bezier(0.55,0.055,0.675,0.19)
    if name == "in-quart"
        return cubic-bezier(0.895,0.03,0.685,0.22)
    if name == "in-quint"
        return cubic-bezier(0.755,0.05,0.855,0.06)
    if name == "in-sine"
        return cubic-bezier(0.47,0,0.745,0.715)
    if name == "in-expo"
        return cubic-bezier(0.95,0.05,0.795,0.035)
    if name == "in-circ"
        return cubic-bezier(0.6,0.04,0.98,0.335)
    if name == "in-back"
        return cubic-bezier(0.6,-0.28,0.735,0.045)
    if name == "out-quad"
        return cubic-bezier(0.25,0.46,0.45,0.94)
    if name == "out-cubic"
        return cubic-bezier(0.215,0.61,0.355,1)
    if name == "out-quart"
        return cubic-bezier(0.165,0.84,0.44,1)
    if name == "out-quint"
        return cubic-bezier(0.23,1,0.32,1)
    if name == "out-sine"
        return cubic-bezier(0.39,0.575,0.565,1)
    if name == "out-expo"
        return cubic-bezier(0.19,1.00,0.22,1)
    if name == "out-circ"
        return cubic-bezier(0.075,0.82,0.165,1)
    if name == "out-back"
        return cubic-bezier(0.175,0.885,0.32,1.275)
    if name == "in-out-quad"
        return cubic-bezier(0.455,0.03,0.515,0.955)
    if name == "in-out-cubic"
        return cubic-bezier(0.645,0.045,0.355,1)
    if name == "in-out-quart"
        return cubic-bezier(0.77,0,0.175,1)
    if name == "in-out-quint"
        return cubic-bezier(0.86,0,0.07,1)
    if name == "in-out-sine"
        return cubic-bezier(0.445,0.05,0.55,0.95)
    if name == "in-out-expo"
        return cubic-bezier(1,0,0,1)
    if name == "in-out-circ"
        return cubic-bezier(0.785,0.135,0.15,0.86)
    if name == "in-out-back"
        return cubic-bezier(0.68,-0.55,0.265,1.55)
    return name

easing = me-easing unless me-no-conflict

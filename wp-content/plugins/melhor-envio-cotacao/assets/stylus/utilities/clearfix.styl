me-clearfix()
    if me-support-ie <= 7
        *zoom 1
    &:before
    &:after
        content: " "
        display: table
    &:after
        clear: both

clearfix = me-clearfix unless me-no-conflict

me-clear( value, args... )
    if value == fix
        clearfix()
    else
        clear: value args

clear = me-clear unless me-no-conflict

_me-fix-processing( current-property )
    if( current-property[ 0 ] == "clear" )
        fix
    else
        error( "fix() must be used within a clear property" )

me-fix()
    if( current-property )
        _me-fix-processing( current-property )
    else
        error( "me-fix() must be used within a clear property" )

unless me-no-conflict
    fix()
        if( current-property )
            _me-fix-processing( current-property )
        else
            error( "fix() must be used within a clear property" )

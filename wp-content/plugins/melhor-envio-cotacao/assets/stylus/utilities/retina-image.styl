me-retina-image( img-src, size = false, suffix = "@2x" )
    ext = extname( img-src )
    image-src = pathjoin( dirname( img-src ), basename( img-src, ext ) + ext )
    retina-src = pathjoin( dirname( img-src ), basename( img-src, ext ) + suffix + ext )
    unless size
        size = image-size( img-src, true )
        if size[ 0 ] == 0 && size[ 1 ] == 0
            size = contain

    background-image: url( image-src )
    +me-retina-query( only screen, 1.3 )
        background-image: url( retina-src )
        background-size: size

retina-image = me-retina-image unless me-no-conflict
me-hidpi-image = me-retina-image unless me-no-conflict
hidpi-image = me-hidpi-image unless me-no-conflict

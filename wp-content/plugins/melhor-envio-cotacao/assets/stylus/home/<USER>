.me-form
  display block

  .options-list
    .inputBox
      margin-top 8px

  .subText
    opacity 0
    color #3598dc
    position absolute
    left 8px
    line-height 1.2em
    font-size 12px
    transition all .5s
    &.-force
      opacity 1

  .title-form
    margin 16px 0 8px
    font-size 14px
    font-weight 600

  .formLine
    margin-bottom 16px
    font-family 'Open Sans'
    transition all .5s
    clear fix

    &.focus
      .subText
        opacity 1

    &.focus
    &.active
      .inputBox
        &:before
          opacity 1
          
    .inputBox
      position relative
      size 100% 46px

      &:before
        opacity 0
        position absolute
        top 10px
        size 40px
        line-height 40px
        font-family 'Open Sans'
        font-size 14px
        color #3b3f51

      &.-textarea
        height 200px

      &.-select
        label
          padding-right 45px
      
      &.-cm
        &:before
          content 'cm'
          text-align right
          right 5px
        input
          padding-right 30px
      &.-kg
        &:before
          content 'kg'
          text-align right
          right 5px
        input
          padding-right 30px

      label
        position absolute
        top 50%
        left 8px
        transform translateY(-50%)
        font-size 14px
        transition all .5s
        color #556976

    textarea
      line-height 1.4em      

    textarea
    input[type="text"]
    input[type="number"]
    input[type="password"]
      size 100%
      padding 16px 8px 0
      line-height 48px
      box-sizing border-box
      color #26303C
      font-size 14px
      border 1px solid transparent
      background #FFFFFF
      box-shadow 0 5px 8px 0 rgba(38,48,60,0.10)
      border-radius 4px
      resize none
      &:focus
        outline none

    .errorText
      opacity 0
      color #e7505a
      position absolute
      left 8px
      padding 0
      margin 4px 0 0 0
      font-size 12px

    &.active
    &.focus
      .inputBox
        label
          font-size 10px
          top 8px
          transform translateY(0)
      textarea
      input[type="text"]
      input[type="number"]
      input[type="password"]
      .ui.search.selection.dropdown
        border-color #0550A0
        box-shadow none

    &.error
      margin-bottom 32px
      textarea
      input[type="text"]
      input[type="number"]
      input[type="password"]
      .ui.search.selection.dropdown
      .dropdown.icon
        border-color #e7505a !important
        box-shadow none
      .errorText
        opacity 1

  .other-line
    clear fix
    .formLine
      &:last-child
        display none
    &.-open
      .formLine
        &:first-child
          width 200px
          float left
        &:last-child
          display block
          float right
          width calc('100% - 210px')


  .ui.search.selection.dropdown
    background none
    border none
    box-sizing border-box
    padding-left 8px
    min-height auto
    height 48px
    border-radius 4px
    color #26303C
    font-size 14px
    border 1px solid transparent
    background #FFFFFF
    box-shadow 0 5px 8px 0 rgba(38,48,60,0.10)
    label
      z-index 1

    &.active
      border-color #0550A0
      box-shadow none
      + label
        font-size 10px
        top 8px
        transform translateY(0)

    .menu > .item
      font-size rem(12px)

    .item
      border-top none !important

    > input.search
    .text
      position absolute
      top 0
      size 100% 40px
      overflow hidden
      padding 24px 8px 8px !important
      font-size 14px
      color #3b3f51
      height auto
      box-sizing border-box

    &.visible
      border-bottom none
      border-bottom-left-radius 0
      border-bottom-right-radius 0

    &.visible
      .menu
        width 100%
        box-shadow none
        border-radius 0 0 4px 4px
        box-sizing border-box
        border-color #0550A0

    .dropdown.icon
      position absolute
      top 0
      right 0
      size 40px 100%
      padding 0
      margin 0
      float none
      box-sizing border-box
      opacity 1
      &:before
        content ''
        position absolute
        display block
        size 12px
        border-bottom 2px solid #0550A0
        border-left 2px solid #0550A0
        border-bottom-left-radius 2px
        top 42%
        left 50%
        transform translate3d(-49%, -50%, 0) rotate(-45deg)

  .checkLine
    padding 5px 0
    margin-bottom 16px
    
  .customCheckBullet
    position absolute
    opacity 0
    & + label
      display block
      position relative
      cursor pointer
      padding-left 45px
      color #697882
      font-family 'Open Sans'
      font-size 12px
    & + label:before
      content ''
      position absolute
      top 50%
      left 0
      transform translateY(-50%)
      size 32px 12px
      border 1px solid #697882
      background #ffffff
      border-radius 12px
      transition all 1s
    & + label:after
      content ''
      position absolute
      top 50%
      left 0
      transform translateY(-50%)
      size 14px
      background #ffffff
      border-radius 50%
      border 1px solid #697882
      box-shadow none
      transition left .5s
    &:checked + label:after
      background endeavour
      left 14px
    &:checked + label:before
      border-color endeavour

  .selectLine
    margin-bottom 16px    
    label
      font-family 'Open Sans'
      font-size 12px
      color #697882
      display block
      position relative
      cursor pointer
      padding 0 0 0 24px
      transition all .5s
      font-weight 500
    input
      position absolute
      opacity 0
      & + label:before
        content ''
        box-sizing border-box
        top 50%
        left 0
        transform translateY(-50%)
        position absolute
        size 17px
        border 1px solid #666666
        border-radius 4px
      & + label:after
        content ''
        position absolute
        top 2px
        left 4px
        transform rotate(-45deg)
        border-left 2px solid endeavour
        border-bottom 2px solid endeavour
        size 8px 4px
        opacity 0
        transition all .5s
      &:checked + label:after
        opacity 1
      &:checked + label
        color #26303C
        font-weight 600

  .formBox
    clear fix
    .col-2
      column(1/2, $cycle: 2)

    +below(940px)
      .col-2
        column(1/1, $cycle: 1)


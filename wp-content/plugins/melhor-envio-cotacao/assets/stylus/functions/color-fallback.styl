// color-fallback( color )
// duplicate current property to create color fallback for colors with alpha values < 1

use( "color-fallback.js" )

_me-color-fallback-processing( current-property, color )
    if( alpha( color ) < 1 )
        fallback-color-value = alpha( color, 1 )
        add-property( current-property[ 0 ], _replace( current-property[ 1 ], "__CALL__", fallback-color-value ) )

me-color-fallback( color )
    if( current-property )
        _me-color-fallback-processing( current-property, color )
        color
    else
        error( "me-color-fallback() must be used within a property" )

unless me-no-conflict
    color-fallback( color )
        if( current-property )
            _me-color-fallback-processing( current-property, color )
            color
        else
            error( "color-fallback() must be used within a property" )

// analogous( color )
// returns analogous variations of given color

me-analogous( color )
    first = spin( color, 30 )
    second = spin( color, -30 )
    ( first second )

analogous = me-analogous unless me-no-conflict

// extended-analogous( color )
// returns extended analogous variations (4 instead of 2) of given color

me-extended-analogous( color )
    first = spin( color, 30 )
    second = spin( color, -30 )
    third = spin( color, 60 )
    fourth = spin( color, -60 )
    ( first second third fourth )

extended-analogous = me-extended-analogous unless me-no-conflict

.me-form
  display block

  .options-list
    .inputBox
      margin-top 8px

  .subText
    opacity 0
    color #3598dc
    position absolute
    left 8px
    line-height 1.2em
    font-size 12px
    transition all .5s
    &.-force
      opacity 1

  .title-form
    margin 16px 0 8px
    font-size 14px
    font-weight 600

  .formLine
    margin-bottom 16px
    font-family 'Open Sans'
    transition all .5s
    clear fix

    &.focus
      .subText
        opacity 1

    &.focus
    &.active
      .inputBox
        &:before
          opacity 1
          
    .inputBox
      position relative
      size 100% 46px

      &:before
        opacity 0
        position absolute
        top 10px
        size 40px
        line-height 40px
        font-family openSans
        font-size 14px
        color #3b3f51


      &.-textarea
        height 200px

      &.-select
        label
          padding-right 45px
      
      &.-cm
        &:before
          content 'cm'
          text-align right
          right 5px
        input
          padding-right 30px
      &.-kg
        &:before
          content 'kg'
          text-align right
          right 5px
        input
          padding-right 30px

      label
        position absolute
        top 50%
        left 8px
        transform translateY(-50%)
        font-size 14px
        transition all .5s
        color #556976

    textarea
      line-height 1.4em      

    textarea
    input[type="text"]
    input[type="number"]
    input[type="password"]
      size 100%
      padding 16px 8px 0
      line-height 46px
      box-sizing border-box
      border none
      font-size 16px
      color #3b3f51
      background transparent
      border-bottom 1px solid #0550a0
      border-left 1px solid #0550a0
      border-bottom-left-radius 4px
      resize none

    .errorText
      opacity 0
      color #e7505a
      position absolute
      left 8px
      padding 0
      margin 0
      font-size 12px

    &.active
    &.focus
      .inputBox
        label
          font-size 10px
          top 4px
          transform translateY(0)

    &.error
      margin-bottom 32px
      textarea
      input[type="text"]
      input[type="number"]
      input[type="password"]
      .ui.search.selection.dropdown
      .dropdown.icon
        border-color #e7505a !important
      .errorText
        opacity 1

  .other-line
    clear fix
    .formLine
      &:last-child
        display none
    &.-open
      .formLine
        &:first-child
          width 200px
          float left
        &:last-child
          display block
          float right
          width calc('100% - 210px')


  .ui.search.selection.dropdown
    background none
    border none
    box-sizing border-box
    padding-left 8px
    min-height auto
    height 46px
    z-index 2
    border 1px solid #0550a0 !important
    border-radius 4px
    label
      z-index 1

    &.active
      + label
        font-size 10px
        top 4px
        transform translateY(0)

    .menu > .item
      font-size rem(12px)

    .item
      border-top none !important

    > input.search
    .text
      position absolute
      top 0
      size 100% 40px
      overflow hidden
      padding 16px 8px 0 !important
      font-size 16px
      color #3b3f51
      height auto
      box-sizing border-box

    &.visible
      border-bottom none
      border-bottom-left-radius 0
      border-bottom-right-radius 0

    &.visible
      .dropdown.icon
        border-bottom 1px solid #0550a0
      .menu
        background #ffffff
        border 1px solid #0550a0 !important
        border-width 0 1px 1px 1px !important
        width 100%
        margin 0 -1px
        box-shadow none
        border-radius 0 0 4px 4px
        box-sizing border-box

    .dropdown.icon
      position absolute
      top 0
      right 0
      size 40px 100%
      padding 0
      margin 0
      float none
      border-left 1px solid #0550a0
      box-sizing border-box
      &:before
        content ''
        position absolute
        display block
        size 12px
        border-bottom 1px solid #0550a0
        border-left 1px solid #0550a0
        border-bottom-left-radius 2px
        top 42%
        left 50%
        transform translate3d(-49%, -50%, 0) rotate(-45deg)

  .checkLine
    padding 5px 0
    
  .customCheckBullet
    position absolute
    opacity 0
    & + label
      display block
      position relative
      cursor pointer
      padding-left 45px
      color brightGray
      font-family 'Open Sans'
      font-size 16px
    & + label:before
      content ''
      position absolute
      top 50%
      left 0
      transform translateY(-50%)
      size 32px 12px
      border 1px solid brightGray
      background #ffffff
      border-radius 12px
      transition all 1s
    & + label:after
      content ''
      position absolute
      top 50%
      left 0
      transform translateY(-50%)
      size 16px
      background #ffffff
      border-radius 50%
      border 1px solid brightGray
      box-shadow none
      transition left .5s
    &:checked + label:after
      background endeavour
      left 16px
    &:checked + label:before
      border-color endeavour

  .paddingBox
    padding 5%

  .formBox
    clear fix
    .col-2
      column(1/2, $cycle: 2)

    +below(940px)
      .col-2
        column(1/1, $cycle: 1)



.table-box-abas
  list-style none
  padding 0
  margin 0
  position relative
  margin-bottom -1px
  li
    display inline-block
    vertical-align bottom
    &.on
      background #ffffff
      font-size 16px
      border-left 1px solid #c8cdda
      border-right 1px solid #c8cdda
      border-top 1px solid #c8cdda
      border-top-left-radius 4px
      border-top-right-radius 4px
      a
        color #0550A0

    a
      display block
      font-size 14px
      padding 20px 60px
      font-weight 600
      text-align center
      text-transform uppercase
      color #626262
      text-decoration none

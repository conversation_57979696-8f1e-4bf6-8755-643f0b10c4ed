_me-linear-gradient-processing( current-property, start, props )
    if( current-property[ 0 ] == "background-image" )
        steps = ()
        compat-steps = ()
        if( length( start ) == 1 )
            if( start is a "color" )
                push( steps, start )
                push( compat-steps, start )
            else
                push( steps, start )
                if( unit( start ) == "deg" )
                    push( compat-steps, start + 180deg )
                else
                    push( compat-steps, start )
        else
            push( steps, join( " ", start ) )
            if( start[ 0 ] == "to" )
                shift( start )
                push( compat-steps, join( " ", opposite-position( start ) ) )
            else
                push( compat-steps, join( " ", start ) )

        for prop in props
            push( steps, join( " ", prop ) )
            push( compat-steps, join( " ", prop ) )

        add-property( "background-image", unquote( "-webkit-linear-gradient(" + join( ",", compat-steps ) + ")" ) )
        add-property( "background-image", unquote( "-moz-linear-gradient(" + join( ",", compat-steps ) + ")" ) )
        add-property( "background-image", unquote( "-ms-linear-gradient(" + join( ",", compat-steps ) + ")" ) )
        add-property( "background-image", unquote( "-o-linear-gradient(" + join( ",", compat-steps ) + ")" ) )
        unquote( "linear-gradient(" + join( ",", steps ) + ")" )
    else
        error( "linear-gradient() must be used within a 'background-image' property" )

me-linear-gradient( start, props... )
    if( current-property )
        _me-linear-gradient-processing( current-property, start, props )
    else
        error( "me-linear-gradient() must be used within a 'background-image' property" )

unless me-no-conflict
    linear-gradient( start, props... )
        if( current-property )
            _me-linear-gradient-processing( current-property, start, props )
        else
            error( "linear-gradient() must be used within a 'background-image' property" )

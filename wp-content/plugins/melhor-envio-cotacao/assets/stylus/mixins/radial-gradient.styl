_me-radial-gradient-processing( current-property, props )
    if( current-property[ 0 ] == "background-image" )
        add-property( "background-image", unquote( "-webkit-radial-gradient(" + join( ",", props ) + ")" ) )
        add-property( "background-image", unquote( "-moz-radial-gradient(" + join( ",", props ) + ")" ) )
        add-property( "background-image", unquote( "-ms-radial-gradient(" + join( ",", props ) + ")" ) )
        add-property( "background-image", unquote( "-o-radial-gradient(" + join( ",", props ) + ")" ) )
        unquote( "radial-gradient(" + join( ",", props ) + ")" )
    else
        error( "radial-gradient() must be used within a 'background-image' property" )

me-radial-gradient( props... )
    if( current-property )
        _me-radial-gradient-processing( current-property, props )
    else
        error( "me-radial-gradient() must be used within a 'background-image' property" )

unless me-no-conflict
    radial-gradient( props... )
        if( current-property )
            _me-radial-gradient-processing( current-property, props )
        else
            error( "radial-gradient() must be used within a 'background-image' property" )

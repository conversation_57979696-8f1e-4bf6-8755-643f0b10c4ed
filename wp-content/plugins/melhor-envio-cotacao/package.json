{"name": "vue-wp-starter", "version": "1.0.0", "description": "A WordPress Vue.js starter plugin", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "GPLv2", "private": true, "engines": {"node": ">=14.18.0"}, "scripts": {"dev": "cross-env WEBPACK_ENV=development webpack --progress --color --watch", "dev-build": "cross-env WEBPACK_ENV=development webpack", "build": "cross-env WEBPACK_ENV=production webpack"}, "dependencies": {"axios": "^0.21.2", "jeet": "^7.2.0", "lodash": "^4.17.21", "rupture": "^0.7.1", "v-money": "^0.8.1", "vue": "^2.6.11", "vue-router": "^3.1.6", "vue-the-mask": "^0.11.1", "cross-env": "^5.2.1", "vuex": "^3.3.0", "@braintree/sanitize-url": "^7.0.3"}, "devDependencies": {"@babel/cli": "^7.17.10", "@babel/core": "^7.18.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/preset-env": "^7.18.0", "@vue/compiler-sfc": "^3.2.34", "babel-loader": "^8.2.5", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.24.1", "babel-runtime": "^6.26.0", "browser-sync": "^2.26.7", "browser-sync-webpack-plugin": "^2.2.2", "css-loader": "^5.2.7", "css-minimizer-webpack-plugin": "^4.0.0", "file-loader": "^6.2.0", "mini-css-extract-plugin": "2.6.0", "path-browserify": "^1.0.1", "stylus": "^0.54.5", "stylus-loader": "^4.3.3", "terser-webpack-plugin": "^5.3.1", "vue-loader": "^15.9.8", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.6.14", "webpack": "^5.72.1", "webpack-cli": "^4.9.2"}}